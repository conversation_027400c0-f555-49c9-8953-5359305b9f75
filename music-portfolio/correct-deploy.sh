#!/bin/bash

echo "🎯 正确部署脚本 - 保持原始效果"
echo "================================="

# 检查是否有备份
if [ -d "backup" ]; then
    echo "1. 恢复原始文件..."
    cp -r backup/src . 2>/dev/null || true
    cp backup/package.json . 2>/dev/null || true
    echo "✅ 原始文件已恢复"
fi

echo "2. 恢复原始 package.json 配置..."
if [ -f "package.legacy.json" ]; then
    echo "✅ 找到原始配置，正在恢复..."
    cp package.legacy.json package.json
    echo "✅ 原始 package.json 已恢复"
else
    echo "⚠️ 未找到 package.legacy.json，使用当前配置"
fi

# 显示当前依赖
echo "📦 当前项目依赖:"
grep -A 15 '"dependencies"' package.json

echo ""
echo "3. 修复 TypeScript 配置（保持功能不变）..."
echo "   问题: moduleResolution: 'bundler' 不被 Next.js 13.5.6 支持"
echo "   解决: 改为 moduleResolution: 'node'"

# 备份当前 tsconfig.json
cp tsconfig.json tsconfig.json.backup

# 修复 moduleResolution 问题
sed -i 's/"moduleResolution": "bundler"/"moduleResolution": "node"/' tsconfig.json

echo "✅ TypeScript 配置已修复"

echo "4. 修复 Next.js 配置（兼容 Next.js 13.5.6）..."
echo "   问题: next.config.ts 不被 Next.js 13.5.6 支持"
echo "   解决: 转换为 next.config.js"

# 备份并删除 TypeScript 配置文件
if [ -f "next.config.ts" ]; then
    cp next.config.ts next.config.ts.backup
    rm next.config.ts
fi

# 创建兼容的 JavaScript 配置
cat > next.config.js << 'EOF'
/** @type {import('next').NextConfig} */
const nextConfig = {
  // 基本配置
  reactStrictMode: true,
  swcMinify: true,

  // 图片配置
  images: {
    domains: ['localhost'],
  },

  // 兼容性配置
  typescript: {
    ignoreBuildErrors: false,
  },
  eslint: {
    ignoreDuringBuilds: false,
  },
}

module.exports = nextConfig
EOF

echo "✅ Next.js 配置已修复"

echo "5. 检查 Node.js 版本兼容性..."
node_version=$(node --version | cut -d'v' -f2)
echo "当前 Node.js 版本: v$node_version"

# 检查版本是否满足要求
if [ "$(printf '%s\n' "18.0.0" "$node_version" | sort -V | head -n1)" = "18.0.0" ]; then
    echo "✅ Node.js 版本满足要求"
else
    echo "⚠️ Node.js 版本可能过低，建议升级到 18+"
    echo "但继续尝试部署..."
fi

echo "6. 清理缓存..."
rm -rf node_modules package-lock.json .next
npm cache clean --force 2>/dev/null || true

echo "7. 安装完整依赖（保持原始功能）..."
echo "正在安装: lucide-react, @supabase/supabase-js, react-audio-player, tailwindcss..."

# 使用兼容模式安装
npm install --legacy-peer-deps

if [ $? -ne 0 ]; then
    echo "❌ 标准安装失败，尝试强制安装..."
    npm install --force --legacy-peer-deps
    
    if [ $? -ne 0 ]; then
        echo "❌ 强制安装也失败，尝试逐个安装关键依赖..."
        
        # 逐个安装核心依赖
        npm install next@15.4.6 react@19.1.0 react-dom@19.1.0 --legacy-peer-deps
        npm install lucide-react@^0.539.0 --legacy-peer-deps
        npm install @supabase/supabase-js@^2.55.0 --legacy-peer-deps
        npm install react-audio-player@^0.17.0 --legacy-peer-deps
        npm install tailwindcss@^4 --legacy-peer-deps
        npm install typescript@^5 @types/node@^20 @types/react@^19 @types/react-dom@^19 --save-dev --legacy-peer-deps
        
        if [ $? -ne 0 ]; then
            echo "❌ 依赖安装彻底失败"
            exit 1
        fi
    fi
fi

echo "8. 验证关键依赖是否安装成功..."
if [ -d "node_modules/lucide-react" ]; then
    echo "✅ lucide-react 安装成功"
else
    echo "❌ lucide-react 安装失败"
    npm install lucide-react --legacy-peer-deps --force
fi

if [ -d "node_modules/@supabase" ]; then
    echo "✅ @supabase/supabase-js 安装成功"
else
    echo "❌ @supabase/supabase-js 安装失败"
    npm install @supabase/supabase-js --legacy-peer-deps --force
fi

if [ -d "node_modules/tailwindcss" ]; then
    echo "✅ tailwindcss 安装成功"
else
    echo "❌ tailwindcss 安装失败"
    npm install tailwindcss --legacy-peer-deps --force
fi

echo "9. 检查项目文件结构..."
echo "📁 项目结构:"
find src -name "*.tsx" -o -name "*.ts" | head -10

echo ""
echo "10. 构建项目（保持原始功能）..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ 构建成功！所有原始功能保持不变"
    
    # 停止旧进程
    pkill -f "next" 2>/dev/null || true
    if [ -f "app.pid" ]; then
        kill $(cat app.pid) 2>/dev/null || true
        rm app.pid
    fi
    
    echo "11. 启动应用..."
    nohup npm start > app.log 2>&1 &
    echo $! > app.pid
    
    sleep 5
    
    if ps -p $(cat app.pid) > /dev/null; then
        echo ""
        echo "🎉 部署成功！原始功能完整保留！"
        echo "================================="
        echo "📊 部署信息:"
        echo "- 应用状态: ✅ 运行中"
        echo "- PID: $(cat app.pid)"
        echo "- 端口: 3000"
        echo "- 本地访问: http://localhost:3000"
        echo "- 外部访问: http://$(hostname -I | awk '{print $1}'):3000"
        echo ""
        echo "🎵 功能确认:"
        echo "- ✅ Lucide React 图标"
        echo "- ✅ Supabase 数据库"
        echo "- ✅ 音频播放器"
        echo "- ✅ Tailwind CSS 样式"
        echo "- ✅ 所有原始页面和组件"
        echo ""
        echo "🛠️ 管理命令:"
        echo "- 查看日志: tail -f app.log"
        echo "- 停止应用: kill $(cat app.pid)"
        echo "- 重启应用: ./correct-deploy.sh"
        echo ""
        
        # 测试应用响应
        sleep 2
        if curl -s http://localhost:3000 > /dev/null; then
            echo "🔍 应用测试: ✅ 响应正常"
            echo "🎊 恭喜！你的音乐作品集已成功部署，功能完整！"
        else
            echo "🔍 应用测试: ⚠️ 正在启动中，请稍等..."
        fi
        
    else
        echo "❌ 应用启动失败"
        echo "查看详细日志: cat app.log"
        exit 1
    fi
    
else
    echo "❌ 构建失败，查看错误信息..."
    echo ""
    echo "🔧 可能的解决方案:"
    echo "1. 检查 Node.js 版本: node --version"
    echo "2. 升级 Node.js: curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash - && sudo apt-get install -y nodejs"
    echo "3. 清理重试: rm -rf node_modules package-lock.json && npm install"
    exit 1
fi
