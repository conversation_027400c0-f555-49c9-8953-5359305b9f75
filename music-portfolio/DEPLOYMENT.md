# 部署指南

## 本地开发

1. **安装依赖**
   ```bash
   npm install
   ```

2. **配置环境变量**
   ```bash
   cp .env.local.example .env.local
   ```
   编辑 `.env.local` 文件，设置管理员账号密码。

3. **启动开发服务器**
   ```bash
   npm run dev
   ```

4. **访问网站**
   - 前台: http://localhost:3000
   - 后台管理: http://localhost:3000/admin

## 生产部署

### Vercel 部署 (推荐)

1. **推送代码到 GitHub**
   ```bash
   git init
   git add .
   git commit -m "Initial commit"
   git remote add origin your-github-repo-url
   git push -u origin main
   ```

2. **在 Vercel 中导入项目**
   - 访问 https://vercel.com
   - 点击 "New Project"
   - 导入您的 GitHub 仓库

3. **配置环境变量**
   在 Vercel 项目设置中添加环境变量：
   - `ADMIN_USERNAME`: 管理员用户名
   - `ADMIN_PASSWORD`: 管理员密码

4. **部署**
   Vercel 会自动构建和部署您的项目。

### 自建服务器部署

#### 快速部署（推荐）

1. **上传项目文件到服务器**
   ```bash
   # 使用 scp 或 git clone 上传项目
   git clone your-repo-url
   cd music-portfolio
   ```

2. **运行部署脚本**
   ```bash
   chmod +x deploy.sh
   ./deploy.sh
   ```

#### 手动部署步骤

1. **安装 Node.js (推荐 18+ 或 20+)**
   ```bash
   # Ubuntu/Debian
   curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
   sudo apt-get install -y nodejs

   # CentOS/RHEL
   curl -fsSL https://rpm.nodesource.com/setup_20.x | sudo bash -
   sudo yum install -y nodejs
   ```

2. **安装项目依赖**
   ```bash
   npm install
   ```

3. **构建生产版本**
   ```bash
   npm run build
   ```

4. **启动应用**
   ```bash
   # 直接启动
   npm run start

   # 或使用 PM2 (推荐)
   npm install -g pm2
   pm2 start npm --name "music-portfolio" -- start
   pm2 startup
   pm2 save
   ```

5. **配置反向代理 (可选)**

   使用 Nginx 配置反向代理：
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;

       location / {
           proxy_pass http://localhost:3000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

### 其他平台部署

项目支持部署到任何支持 Node.js 的平台：

- **Netlify**: 支持 Next.js 部署
- **Railway**: 简单的 Node.js 部署
- **DigitalOcean App Platform**: 容器化部署
- **Docker**: 容器化部署

## 自定义配置

### 1. 替换头像图片
将您的头像图片命名为 `avatar.jpg` 并放置在 `public/` 目录下。

### 2. 添加媒体文件
创建以下目录结构：
```
public/
├── posters/          # 电影海报图片
├── thumbnails/       # 视频缩略图
└── music/           # 音乐文件 (如果需要)
```

### 3. 配置内容
- **音乐视频**: 在后台管理系统中添加B站视频链接
- **电影信息**: 在后台管理系统中添加电影详情和海报
- **分类管理**: 支持自定义电影分类

### 4. 数据库集成 (可选)
如需使用真实数据库而非模拟数据：

1. 注册 Supabase 账号
2. 创建新项目
3. 在 `.env.local` 中配置 Supabase 连接信息
4. 创建数据表（参考 `src/lib/supabase.ts` 中的类型定义）

## 维护

### 更新内容
- **音乐管理**: 通过后台管理系统添加/编辑/删除B站音乐视频
- **电影管理**: 通过后台管理系统管理电影信息和海报
- **文件上传**: 支持海报和缩略图上传
- **分类管理**: 可以自定义电影分类

### 备份
- 定期备份 `.env.local` 文件
- 如使用数据库，定期备份数据库

### 监控
- 使用 Vercel Analytics 监控网站访问情况
- 检查服务器日志排查问题

## 故障排除

### 常见问题

1. **Node.js 版本过低错误**

   **问题原因**: 服务器上的 Node.js 版本低于项目要求（需要 14+）

   **解决方案**:
   ```bash
   # 方法1: 使用自动升级脚本（推荐）
   chmod +x upgrade-nodejs.sh
   ./upgrade-nodejs.sh

   # 方法2: 手动升级（Ubuntu/Debian）
   curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
   sudo apt-get install -y nodejs

   # 方法3: 手动升级（CentOS/RHEL）
   curl -fsSL https://rpm.nodesource.com/setup_20.x | sudo bash -
   sudo yum install -y nodejs

   # 验证版本
   node --version  # 应该显示 v18+ 或 v20+
   ```

2. **模块找不到错误 (Cannot find module '../server/require-hook')**

   **问题原因**: 在生产服务器上使用了开发模式命令或构建文件不完整

   **解决方案**:
   ```bash
   # 清理并重新构建
   rm -rf .next node_modules package-lock.json
   npm cache clean --force
   npm install
   npm run build

   # 使用生产模式启动（不要用 npm run dev）
   npm run start

   # 或使用提供的部署脚本（会自动处理版本兼容性）
   chmod +x deploy.sh
   ./deploy.sh
   ```

2. **字体加载失败**
   - 已使用系统字体替代，无需额外配置

3. **图片无法显示**
   - 检查图片文件是否存在于 `public/` 目录
   - 确认图片文件名和路径正确

4. **B站视频无法播放**
   - 检查B站视频链接是否正确
   - 确认视频是否公开可访问
   - 支持的链接格式：
     - `https://www.bilibili.com/video/BV...`
     - `https://www.bilibili.com/video/av...`
     - `https://b23.tv/...`

5. **文件上传失败**
   - 检查文件大小是否超过5MB
   - 确认文件格式为JPEG、PNG或WebP
   - 检查服务器写入权限

### 获取帮助
如遇到问题，请检查：
1. 浏览器控制台错误信息
2. 服务器日志
3. 网络连接状态
