# 使用说明

## 网站功能概览

### 前台页面
1. **主页 (/)** - KIMAHALA品牌展示，包含电吉他背景
2. **音乐页面 (/music)** - 展示音乐作品，支持在线播放
3. **视频页面 (/video)** - 展示B站视频，支持嵌入播放

### 后台管理 (/admin)
- **音乐管理** - 添加、删除音乐作品
- **视频管理** - 添加、删除B站视频
- **个人信息管理** - 编辑个人资料

## 导航栏功能

### 设计特色
- 参考 Joanne <PERSON> Taylor 官网风格
- KIMAHALA 艺术字体 Logo
- 英文导航菜单：HOME, MUSIC, VIDEOS
- 选中状态显示红色，未选中为灰色
- 右侧B站图标链接到：https://space.bilibili.com/27744192

## 后台管理使用指南

### 1. 登录后台
1. 访问 `/admin`
2. 输入用户名：`admin`
3. 输入密码：`admin123`（可在 .env.local 中修改）

### 2. 音乐管理
**添加音乐：**
- 点击"添加音乐"按钮
- 填写必填信息：
  - 歌曲标题 *
  - 演唱者 *
  - 音频文件URL *（如：/music/song.mp3）
- 可选信息：
  - 时长（格式：3:45）
  - 描述

**删除音乐：**
- 点击音乐条目右侧的删除按钮
- 确认删除操作

### 3. 视频管理
**添加视频：**
- 点击"添加视频"按钮
- 填写必填信息：
  - 视频标题 *
  - B站视频链接 *（完整链接，包含BV号）
- 可选信息：
  - 视频描述

**B站链接格式：**
```
https://www.bilibili.com/video/BV1234567890
```
系统会自动提取BV号用于嵌入播放。

**删除视频：**
- 点击视频条目右侧的删除按钮
- 确认删除操作

## 文件上传指南

### 1. 音乐文件
将音乐文件放置在 `public/music/` 目录下：
```
public/
├── music/
│   ├── song1.mp3
│   ├── song2.wav
│   └── song3.ogg
```

支持格式：MP3, WAV, OGG

### 2. 背景图片
将电吉他背景图片命名为 `electric-guitar-bg.jpg` 并放置在 `public/` 目录下：
```
public/
├── electric-guitar-bg.jpg
```

建议尺寸：1920x1080 或更高分辨率

### 3. 封面图片
音乐封面图片放置在 `public/covers/` 目录下：
```
public/
├── covers/
│   ├── cover1.jpg
│   ├── cover2.jpg
│   └── cover3.jpg
```

## API 接口说明

### 公开接口
- `GET /api/music` - 获取音乐列表
- `GET /api/videos` - 获取视频列表

### 管理接口（需要认证）
- `GET /api/admin/music` - 获取音乐列表
- `POST /api/admin/music` - 添加音乐
- `DELETE /api/admin/music?id=xxx` - 删除音乐
- `GET /api/admin/videos` - 获取视频列表
- `POST /api/admin/videos` - 添加视频
- `DELETE /api/admin/videos?id=xxx` - 删除视频

## 数据存储

目前使用内存存储（模拟数据），重启服务器后数据会重置。

如需持久化存储，可以：
1. 集成 Supabase 数据库
2. 使用本地 JSON 文件存储
3. 连接其他数据库服务

## 样式定制

### 颜色主题
- 主色调：红色 (#DC2626)
- 背景：深灰色系
- 文字：白色/灰色

### 字体
- Logo：serif italic 字体
- 导航：semibold tracking-wider
- 正文：系统字体栈

## 部署注意事项

1. **环境变量配置**
   ```env
   ADMIN_USERNAME=admin
   ADMIN_PASSWORD=your_secure_password
   ```

2. **静态文件**
   确保上传所有必要的图片和音频文件到 `public/` 目录

3. **B站链接**
   更新所有B站链接为实际的频道地址

## 故障排除

### 常见问题
1. **音频无法播放** - 检查文件路径和格式
2. **视频无法嵌入** - 确认B站链接格式正确
3. **后台无法登录** - 检查环境变量配置
4. **图片无法显示** - 确认文件存在于 public 目录

### 开发调试
查看浏览器控制台和服务器日志获取详细错误信息。
