#!/bin/bash

echo "🔧 强力修复部署脚本"
echo "===================="

# 1. 完全清理 Node.js 安装
echo "1. 清理现有 Node.js 安装..."
sudo apt-get remove --purge -y nodejs npm node-gyp
sudo apt-get autoremove -y
sudo rm -rf /usr/local/bin/npm /usr/local/share/man/man1/node* /usr/local/lib/dtrace/node.d
sudo rm -rf ~/.npm
sudo rm -rf /usr/local/lib/node*
sudo rm -rf /usr/local/include/node*
sudo rm -rf /usr/local/bin/node*

# 2. 清理 dpkg 缓存
echo "2. 清理包管理器缓存..."
sudo dpkg --configure -a
sudo apt-get clean
sudo apt-get update

# 3. 使用 snap 安装 Node.js（更可靠）
echo "3. 使用 snap 安装 Node.js..."
if command -v snap &> /dev/null; then
    sudo snap install node --classic
    
    # 创建软链接
    sudo ln -sf /snap/bin/node /usr/bin/node
    sudo ln -sf /snap/bin/npm /usr/bin/npm
    sudo ln -sf /snap/bin/npx /usr/bin/npx
else
    echo "snap 不可用，尝试使用 nvm..."
    
    # 4. 使用 nvm 安装（备选方案）
    curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"
    
    nvm install 18
    nvm use 18
    nvm alias default 18
fi

# 5. 验证安装
echo "5. 验证 Node.js 安装..."
node_version=$(node --version 2>/dev/null || echo "失败")
npm_version=$(npm --version 2>/dev/null || echo "失败")

echo "Node.js 版本: $node_version"
echo "npm 版本: $npm_version"

if [[ "$node_version" == "失败" ]] || [[ "$npm_version" == "失败" ]]; then
    echo "❌ Node.js 安装失败，请手动安装"
    exit 1
fi

echo "✅ Node.js 安装成功！"

# 6. 清理项目并使用最简化配置
echo "6. 准备项目部署..."
cd /home/<USER>

# 备份原始文件
cp package.json package.json.original

# 创建最简化的 package.json
cat > package.json << 'EOF'
{
  "name": "music-portfolio",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint"
  },
  "dependencies": {
    "next": "13.5.6",
    "react": "18.2.0",
    "react-dom": "18.2.0",
    "lucide-react": "0.263.1",
    "react-audio-player": "0.17.0"
  },
  "devDependencies": {
    "@types/node": "18.17.0",
    "@types/react": "18.2.0",
    "@types/react-dom": "18.2.0",
    "eslint": "8.48.0",
    "eslint-config-next": "13.5.6",
    "tailwindcss": "3.3.0",
    "typescript": "4.9.5"
  }
}
EOF

echo "7. 清理并重新安装依赖..."
rm -rf node_modules package-lock.json .next
npm cache clean --force

# 使用 --legacy-peer-deps 解决依赖冲突
npm install --legacy-peer-deps

echo "8. 构建项目..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ 构建成功！"
    echo ""
    echo "启动应用..."
    
    # 使用 nohup 在后台启动
    nohup npm start > app.log 2>&1 &
    echo $! > app.pid
    
    echo "应用已在后台启动"
    echo "PID: $(cat app.pid)"
    echo "日志文件: app.log"
    echo "访问地址: http://your-server-ip:3000"
    echo ""
    echo "查看日志: tail -f app.log"
    echo "停止应用: kill $(cat app.pid)"
    
else
    echo "❌ 构建失败"
    echo "恢复原始配置..."
    mv package.json.original package.json
    exit 1
fi

echo "🎉 部署完成！"
