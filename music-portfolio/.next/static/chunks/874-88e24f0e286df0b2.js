"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[874],{2664:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return u}});let n=r(9991),o=r(7102);function u(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,o.hasBasePath)(r.pathname)}catch(e){return!1}}},2757:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return u},formatWithValidation:function(){return a},urlObjectKeys:function(){return i}});let n=r(6966)._(r(8859)),o=/https?|ftp|gopher|file/;function u(e){let{auth:t,hostname:r}=e,u=e.protocol||"",i=e.pathname||"",a=e.hash||"",f=e.query||"",c=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?c=t+e.host:r&&(c=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(c+=":"+e.port)),f&&"object"==typeof f&&(f=String(n.urlQueryToSearchParams(f)));let s=e.search||f&&"?"+f||"";return u&&!u.endsWith(":")&&(u+=":"),e.slashes||(!u||o.test(u))&&!1!==c?(c="//"+(c||""),i&&"/"!==i[0]&&(i="/"+i)):c||(c=""),a&&"#"!==a[0]&&(a="#"+a),s&&"?"!==s[0]&&(s="?"+s),""+u+c+(i=i.replace(/[?#]/g,encodeURIComponent))+(s=s.replace("#","%23"))+a}let i=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function a(e){return u(e)}},3180:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},6874:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return y},useLinkStatus:function(){return b}});let n=r(6966),o=r(5155),u=n._(r(2115)),i=r(2757),a=r(5227),f=r(9818),c=r(6654),s=r(9991),l=r(5929);r(3230);let p=r(4930),d=r(2664),h=r(6634);function g(e){return"string"==typeof e?e:(0,i.formatUrl)(e)}function y(e){let t,r,n,[i,y]=(0,u.useOptimistic)(p.IDLE_LINK_STATUS),b=(0,u.useRef)(null),{href:P,as:E,children:v,prefetch:_=null,passHref:O,replace:j,shallow:T,scroll:N,onClick:C,onMouseEnter:S,onTouchStart:A,legacyBehavior:L=!1,onNavigate:x,ref:U,unstable_dynamicOnHover:I,...k}=e;t=v,L&&("string"==typeof t||"number"==typeof t)&&(t=(0,o.jsx)("a",{children:t}));let w=u.default.useContext(a.AppRouterContext),M=!1!==_,R=null===_||"auto"===_?f.PrefetchKind.AUTO:f.PrefetchKind.FULL,{href:D,as:F}=u.default.useMemo(()=>{let e=g(P);return{href:e,as:E?g(E):e}},[P,E]);L&&(r=u.default.Children.only(t));let K=L?r&&"object"==typeof r&&r.ref:U,B=u.default.useCallback(e=>(null!==w&&(b.current=(0,p.mountLinkInstance)(e,D,w,R,M,y)),()=>{b.current&&((0,p.unmountLinkForCurrentNavigation)(b.current),b.current=null),(0,p.unmountPrefetchableInstance)(e)}),[M,D,w,R,y]),z={ref:(0,c.useMergedRef)(B,K),onClick(e){L||"function"!=typeof C||C(e),L&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),w&&(e.defaultPrevented||function(e,t,r,n,o,i,a){let{nodeName:f}=e.currentTarget;if(!("A"===f.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,d.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}if(e.preventDefault(),a){let e=!1;if(a({preventDefault:()=>{e=!0}}),e)return}u.default.startTransition(()=>{(0,h.dispatchNavigateAction)(r||t,o?"replace":"push",null==i||i,n.current)})}}(e,D,F,b,j,N,x))},onMouseEnter(e){L||"function"!=typeof S||S(e),L&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),w&&M&&(0,p.onNavigationIntent)(e.currentTarget,!0===I)},onTouchStart:function(e){L||"function"!=typeof A||A(e),L&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),w&&M&&(0,p.onNavigationIntent)(e.currentTarget,!0===I)}};return(0,s.isAbsoluteUrl)(F)?z.href=F:L&&!O&&("a"!==r.type||"href"in r.props)||(z.href=(0,l.addBasePath)(F)),n=L?u.default.cloneElement(r,z):(0,o.jsx)("a",{...k,...z,children:t}),(0,o.jsx)(m.Provider,{value:i,children:n})}r(3180);let m=(0,u.createContext)(p.IDLE_LINK_STATUS),b=()=>(0,u.useContext)(m);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8859:(e,t)=>{function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;for(let[r,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)t.append(r,n(e));else t.set(r,n(o));return t}function u(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return u},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return o}})},9991:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return b},MissingStaticPage:function(){return m},NormalizeError:function(){return g},PageNotFoundError:function(){return y},SP:function(){return p},ST:function(){return d},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return f},getLocationOrigin:function(){return i},getURL:function(){return a},isAbsoluteUrl:function(){return u},isResSent:function(){return c},loadGetInitialProps:function(){return l},normalizeRepeatedSlashes:function(){return s},stringifyError:function(){return P}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),u=0;u<n;u++)o[u]=arguments[u];return r||(r=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,u=e=>o.test(e);function i(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function a(){let{href:e}=window.location,t=i();return e.substring(t.length)}function f(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function s(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function l(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await l(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&c(r))return n;if(!n)throw Object.defineProperty(Error('"'+f(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let p="undefined"!=typeof performance,d=p&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class g extends Error{}class y extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class m extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class b extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function P(e){return JSON.stringify({message:e.message,stack:e.stack})}}}]);