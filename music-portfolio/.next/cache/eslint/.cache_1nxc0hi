[{"/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/admin/page.tsx": "1", "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/api/admin/auth/route.ts": "2", "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/api/admin/movies/route.ts": "3", "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/api/admin/music/route.ts": "4", "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/api/admin/videos/route.ts": "5", "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/api/bilibili/route.ts": "6", "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/api/movies/route.ts": "7", "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/api/music/route.ts": "8", "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/api/proxy-image/route.ts": "9", "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/api/upload/route.ts": "10", "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/api/upload/video/route.ts": "11", "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/api/video/[...path]/route.ts": "12", "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/api/videos/route.ts": "13", "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/layout.tsx": "14", "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/movie/page.tsx": "15", "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/music/page.tsx": "16", "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/page.simple.tsx": "17", "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/page.tsx": "18", "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/test/page.tsx": "19", "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/video/page.tsx": "20", "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/components/Footer.tsx": "21", "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/components/Navigation.tsx": "22", "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/components/VideoPlayer.tsx": "23", "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/lib/auth.ts": "24", "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/lib/simple-store.ts": "25", "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/lib/supabase.ts": "26"}, {"size": 35983, "mtime": 1755604731301, "results": "27", "hashOfConfig": "28"}, {"size": 687, "mtime": 1755489055346, "results": "29", "hashOfConfig": "28"}, {"size": 5402, "mtime": 1755605872167, "results": "30", "hashOfConfig": "28"}, {"size": 6020, "mtime": 1755605942600, "results": "31", "hashOfConfig": "28"}, {"size": 3751, "mtime": 1755491342677, "results": "32", "hashOfConfig": "28"}, {"size": 2558, "mtime": 1755513373015, "results": "33", "hashOfConfig": "28"}, {"size": 358, "mtime": 1755511893637, "results": "34", "hashOfConfig": "28"}, {"size": 384, "mtime": 1755511878115, "results": "35", "hashOfConfig": "28"}, {"size": 2320, "mtime": 1755513928670, "results": "36", "hashOfConfig": "28"}, {"size": 2198, "mtime": 1755508369053, "results": "37", "hashOfConfig": "28"}, {"size": 2739, "mtime": 1755574858724, "results": "38", "hashOfConfig": "28"}, {"size": 3555, "mtime": 1755607568001, "results": "39", "hashOfConfig": "28"}, {"size": 1315, "mtime": 1755491371874, "results": "40", "hashOfConfig": "28"}, {"size": 704, "mtime": 1755490622918, "results": "41", "hashOfConfig": "28"}, {"size": 10914, "mtime": 1755577672265, "results": "42", "hashOfConfig": "28"}, {"size": 7121, "mtime": 1755605045979, "results": "43", "hashOfConfig": "28"}, {"size": 6119, "mtime": 1755601015163, "results": "44", "hashOfConfig": "28"}, {"size": 4252, "mtime": 1755509489227, "results": "45", "hashOfConfig": "28"}, {"size": 6816, "mtime": 1755511784663, "results": "46", "hashOfConfig": "28"}, {"size": 5961, "mtime": 1755605515650, "results": "47", "hashOfConfig": "28"}, {"size": 1181, "mtime": 1755490643454, "results": "48", "hashOfConfig": "28"}, {"size": 6402, "mtime": 1755507576202, "results": "49", "hashOfConfig": "28"}, {"size": 9470, "mtime": 1755607600742, "results": "50", "hashOfConfig": "28"}, {"size": 1084, "mtime": 1755489001997, "results": "51", "hashOfConfig": "28"}, {"size": 8601, "mtime": 1755577996852, "results": "52", "hashOfConfig": "28"}, {"size": 1196, "mtime": 1755572732372, "results": "53", "hashOfConfig": "28"}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1uqragz", {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/admin/page.tsx", ["132", "133", "134", "135", "136", "137", "138", "139", "140", "141", "142", "143", "144"], [], "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/api/admin/auth/route.ts", [], [], "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/api/admin/movies/route.ts", ["145", "146", "147", "148", "149"], [], "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/api/admin/music/route.ts", ["150", "151", "152", "153", "154", "155"], [], "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/api/admin/videos/route.ts", ["156", "157", "158"], [], "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/api/bilibili/route.ts", ["159"], [], "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/api/movies/route.ts", [], [], "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/api/music/route.ts", [], [], "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/api/proxy-image/route.ts", [], [], "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/api/upload/route.ts", ["160"], [], "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/api/upload/video/route.ts", [], [], "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/api/video/[...path]/route.ts", ["161", "162"], [], "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/api/videos/route.ts", [], [], "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/layout.tsx", [], [], "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/movie/page.tsx", [], [], "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/music/page.tsx", ["163"], [], "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/page.simple.tsx", [], [], "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/page.tsx", [], [], "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/test/page.tsx", ["164", "165", "166", "167", "168"], [], "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/video/page.tsx", ["169"], [], "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/components/Footer.tsx", [], [], "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/components/Navigation.tsx", [], [], "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/components/VideoPlayer.tsx", ["170"], [], "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/lib/auth.ts", ["171"], [], "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/lib/simple-store.ts", ["172", "173", "174", "175", "176", "177", "178", "179", "180"], [], "/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/lib/supabase.ts", [], [], {"ruleId": "181", "severity": 1, "message": "182", "line": 52, "column": 14, "nodeType": null, "messageId": "183", "endLine": 52, "endColumn": 19}, {"ruleId": "184", "severity": 2, "message": "185", "line": 187, "column": 50, "nodeType": "186", "messageId": "187", "endLine": 187, "endColumn": 53, "suggestions": "188"}, {"ruleId": "184", "severity": 2, "message": "185", "line": 190, "column": 52, "nodeType": "186", "messageId": "187", "endLine": 190, "endColumn": 55, "suggestions": "189"}, {"ruleId": "184", "severity": 2, "message": "185", "line": 254, "column": 30, "nodeType": "186", "messageId": "187", "endLine": 254, "endColumn": 33, "suggestions": "190"}, {"ruleId": "191", "severity": 1, "message": "192", "line": 440, "column": 19, "nodeType": "193", "endLine": 449, "endColumn": 21}, {"ruleId": "184", "severity": 2, "message": "185", "line": 510, "column": 40, "nodeType": "186", "messageId": "187", "endLine": 510, "endColumn": 43, "suggestions": "194"}, {"ruleId": "184", "severity": 2, "message": "185", "line": 513, "column": 52, "nodeType": "186", "messageId": "187", "endLine": 513, "endColumn": 55, "suggestions": "195"}, {"ruleId": "184", "severity": 2, "message": "185", "line": 606, "column": 30, "nodeType": "186", "messageId": "187", "endLine": 606, "endColumn": 33, "suggestions": "196"}, {"ruleId": "184", "severity": 2, "message": "185", "line": 669, "column": 41, "nodeType": "186", "messageId": "187", "endLine": 669, "endColumn": 44, "suggestions": "197"}, {"ruleId": "181", "severity": 1, "message": "198", "line": 675, "column": 22, "nodeType": null, "messageId": "183", "endLine": 675, "endColumn": 23}, {"ruleId": "181", "severity": 1, "message": "198", "line": 682, "column": 22, "nodeType": null, "messageId": "183", "endLine": 682, "endColumn": 23}, {"ruleId": "184", "severity": 2, "message": "185", "line": 705, "column": 21, "nodeType": "186", "messageId": "187", "endLine": 705, "endColumn": 24, "suggestions": "199"}, {"ruleId": "191", "severity": 1, "message": "192", "line": 909, "column": 21, "nodeType": "193", "endLine": 913, "endColumn": 23}, {"ruleId": "181", "severity": 1, "message": "182", "line": 90, "column": 12, "nodeType": null, "messageId": "183", "endLine": 90, "endColumn": 17}, {"ruleId": "184", "severity": 2, "message": "185", "line": 118, "column": 39, "nodeType": "186", "messageId": "187", "endLine": 118, "endColumn": 42, "suggestions": "200"}, {"ruleId": "181", "severity": 1, "message": "182", "line": 178, "column": 12, "nodeType": null, "messageId": "183", "endLine": 178, "endColumn": 17}, {"ruleId": "184", "severity": 2, "message": "185", "line": 206, "column": 39, "nodeType": "186", "messageId": "187", "endLine": 206, "endColumn": 42, "suggestions": "201"}, {"ruleId": "181", "severity": 1, "message": "182", "line": 220, "column": 12, "nodeType": null, "messageId": "183", "endLine": 220, "endColumn": 17}, {"ruleId": "181", "severity": 1, "message": "182", "line": 28, "column": 12, "nodeType": null, "messageId": "183", "endLine": 28, "endColumn": 17}, {"ruleId": "181", "severity": 1, "message": "182", "line": 126, "column": 12, "nodeType": null, "messageId": "183", "endLine": 126, "endColumn": 17}, {"ruleId": "184", "severity": 2, "message": "185", "line": 154, "column": 44, "nodeType": "186", "messageId": "187", "endLine": 154, "endColumn": 47, "suggestions": "202"}, {"ruleId": "181", "severity": 1, "message": "182", "line": 201, "column": 12, "nodeType": null, "messageId": "183", "endLine": 201, "endColumn": 17}, {"ruleId": "184", "severity": 2, "message": "185", "line": 228, "column": 44, "nodeType": "186", "messageId": "187", "endLine": 228, "endColumn": 47, "suggestions": "203"}, {"ruleId": "181", "severity": 1, "message": "182", "line": 240, "column": 12, "nodeType": null, "messageId": "183", "endLine": 240, "endColumn": 17}, {"ruleId": "204", "severity": 2, "message": "205", "line": 5, "column": 5, "nodeType": "206", "messageId": "207", "endLine": 5, "endColumn": 11, "fix": "208"}, {"ruleId": "181", "severity": 1, "message": "182", "line": 95, "column": 12, "nodeType": null, "messageId": "183", "endLine": 95, "endColumn": 17}, {"ruleId": "181", "severity": 1, "message": "182", "line": 133, "column": 12, "nodeType": null, "messageId": "183", "endLine": 133, "endColumn": 17}, {"ruleId": "181", "severity": 1, "message": "182", "line": 21, "column": 12, "nodeType": null, "messageId": "183", "endLine": 21, "endColumn": 17}, {"ruleId": "181", "severity": 1, "message": "182", "line": 55, "column": 14, "nodeType": null, "messageId": "183", "endLine": 55, "endColumn": 19}, {"ruleId": "181", "severity": 1, "message": "209", "line": 5, "column": 26, "nodeType": null, "messageId": "183", "endLine": 5, "endColumn": 37}, {"ruleId": "181", "severity": 1, "message": "198", "line": 28, "column": 18, "nodeType": null, "messageId": "183", "endLine": 28, "endColumn": 19}, {"ruleId": "191", "severity": 1, "message": "192", "line": 116, "column": 19, "nodeType": "193", "endLine": 126, "endColumn": 21}, {"ruleId": "184", "severity": 2, "message": "185", "line": 6, "column": 46, "nodeType": "186", "messageId": "187", "endLine": 6, "endColumn": 49, "suggestions": "210"}, {"ruleId": "184", "severity": 2, "message": "185", "line": 7, "column": 46, "nodeType": "186", "messageId": "187", "endLine": 7, "endColumn": 49, "suggestions": "211"}, {"ruleId": "184", "severity": 2, "message": "185", "line": 8, "column": 56, "nodeType": "186", "messageId": "187", "endLine": 8, "endColumn": 59, "suggestions": "212"}, {"ruleId": "184", "severity": 2, "message": "185", "line": 9, "column": 56, "nodeType": "186", "messageId": "187", "endLine": 9, "endColumn": 59, "suggestions": "213"}, {"ruleId": "214", "severity": 1, "message": "215", "line": 14, "column": 6, "nodeType": "216", "endLine": 14, "endColumn": 8, "suggestions": "217"}, {"ruleId": "184", "severity": 2, "message": "185", "line": 7, "column": 40, "nodeType": "186", "messageId": "187", "endLine": 7, "endColumn": 43, "suggestions": "218"}, {"ruleId": "214", "severity": 1, "message": "219", "line": 79, "column": 6, "nodeType": "216", "endLine": 79, "endColumn": 8, "suggestions": "220"}, {"ruleId": "181", "severity": 1, "message": "182", "line": 28, "column": 12, "nodeType": null, "messageId": "183", "endLine": 28, "endColumn": 17}, {"ruleId": "221", "severity": 2, "message": "222", "line": 12, "column": 16, "nodeType": "223", "messageId": "224", "endLine": 12, "endColumn": 29}, {"ruleId": "184", "severity": 2, "message": "185", "line": 170, "column": 38, "nodeType": "186", "messageId": "187", "endLine": 170, "endColumn": 41, "suggestions": "225"}, {"ruleId": "184", "severity": 2, "message": "185", "line": 182, "column": 60, "nodeType": "186", "messageId": "187", "endLine": 182, "endColumn": 63, "suggestions": "226"}, {"ruleId": "184", "severity": 2, "message": "185", "line": 185, "column": 40, "nodeType": "186", "messageId": "187", "endLine": 185, "endColumn": 43, "suggestions": "227"}, {"ruleId": "184", "severity": 2, "message": "185", "line": 201, "column": 40, "nodeType": "186", "messageId": "187", "endLine": 201, "endColumn": 43, "suggestions": "228"}, {"ruleId": "184", "severity": 2, "message": "185", "line": 226, "column": 33, "nodeType": "186", "messageId": "187", "endLine": 226, "endColumn": 36, "suggestions": "229"}, {"ruleId": "184", "severity": 2, "message": "185", "line": 238, "column": 55, "nodeType": "186", "messageId": "187", "endLine": 238, "endColumn": 58, "suggestions": "230"}, {"ruleId": "184", "severity": 2, "message": "185", "line": 241, "column": 40, "nodeType": "186", "messageId": "187", "endLine": 241, "endColumn": 43, "suggestions": "231"}, {"ruleId": "184", "severity": 2, "message": "185", "line": 257, "column": 40, "nodeType": "186", "messageId": "187", "endLine": 257, "endColumn": 43, "suggestions": "232"}, "@typescript-eslint/no-unused-vars", "'error' is defined but never used.", "unusedVar", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["233", "234"], ["235", "236"], ["237", "238"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["239", "240"], ["241", "242"], ["243", "244"], ["245", "246"], "'e' is defined but never used.", ["247", "248"], ["249", "250"], ["251", "252"], ["253", "254"], ["255", "256"], "prefer-const", "'videos' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "257", "text": "258"}, "'requireAuth' is defined but never used.", ["259", "260"], ["261", "262"], ["263", "264"], ["265", "266"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'runTests'. Either include it or remove the dependency array.", "ArrayExpression", ["267"], ["268", "269"], "React Hook useEffect has a missing dependency: 'togglePlay'. Either include it or remove the dependency array.", ["270"], "@typescript-eslint/no-require-imports", "A `require()` style import is forbidden.", "CallExpression", "noRequireImports", ["271", "272"], ["273", "274"], ["275", "276"], ["277", "278"], ["279", "280"], ["281", "282"], ["283", "284"], ["285", "286"], {"messageId": "287", "fix": "288", "desc": "289"}, {"messageId": "290", "fix": "291", "desc": "292"}, {"messageId": "287", "fix": "293", "desc": "289"}, {"messageId": "290", "fix": "294", "desc": "292"}, {"messageId": "287", "fix": "295", "desc": "289"}, {"messageId": "290", "fix": "296", "desc": "292"}, {"messageId": "287", "fix": "297", "desc": "289"}, {"messageId": "290", "fix": "298", "desc": "292"}, {"messageId": "287", "fix": "299", "desc": "289"}, {"messageId": "290", "fix": "300", "desc": "292"}, {"messageId": "287", "fix": "301", "desc": "289"}, {"messageId": "290", "fix": "302", "desc": "292"}, {"messageId": "287", "fix": "303", "desc": "289"}, {"messageId": "290", "fix": "304", "desc": "292"}, {"messageId": "287", "fix": "305", "desc": "289"}, {"messageId": "290", "fix": "306", "desc": "292"}, {"messageId": "287", "fix": "307", "desc": "289"}, {"messageId": "290", "fix": "308", "desc": "292"}, {"messageId": "287", "fix": "309", "desc": "289"}, {"messageId": "290", "fix": "310", "desc": "292"}, {"messageId": "287", "fix": "311", "desc": "289"}, {"messageId": "290", "fix": "312", "desc": "292"}, {"messageId": "287", "fix": "313", "desc": "289"}, {"messageId": "290", "fix": "314", "desc": "292"}, [139, 1083], "const videos = [\n  {\n    id: '1',\n    title: '《夜空中最亮的星》创作过程分享',\n    description: '分享这首歌的创作灵感和制作过程，从词曲创作到录音制作的完整流程。',\n    bilibili_url: 'https://www.bilibili.com/video/BV1234567890',\n    bilibili_embed_id: 'BV1234567890',\n    thumbnail_url: '/thumbnails/video1.jpg',\n    created_at: '2024-01-15',\n    updated_at: '2024-01-15',\n  },\n  {\n    id: '2',\n    title: '吉他弹唱《时光倒流》',\n    description: '用吉他弹唱这首关于回忆的歌曲，希望能唤起大家心中美好的回忆。',\n    bilibili_url: 'https://www.bilibili.com/video/BV2345678901',\n    bilibili_embed_id: 'BV2345678901',\n    thumbnail_url: '/thumbnails/video2.jpg',\n    created_at: '2024-01-10',\n    updated_at: '2024-01-10',\n  },\n  {\n    id: '3',\n    title: '音乐制作软件使用教程',\n    description: '分享我常用的音乐制作软件和技巧，适合音乐制作初学者观看学习。',\n    bilibili_url: 'https://www.bilibili.com/video/BV3456789012',\n    bilibili_embed_id: 'BV3456789012',\n    thumbnail_url: '/thumbnails/video3.jpg',\n    created_at: '2024-01-05',\n    updated_at: '2024-01-05',\n  },\n]", {"messageId": "287", "fix": "315", "desc": "289"}, {"messageId": "290", "fix": "316", "desc": "292"}, {"messageId": "287", "fix": "317", "desc": "289"}, {"messageId": "290", "fix": "318", "desc": "292"}, {"messageId": "287", "fix": "319", "desc": "289"}, {"messageId": "290", "fix": "320", "desc": "292"}, {"messageId": "287", "fix": "321", "desc": "289"}, {"messageId": "290", "fix": "322", "desc": "292"}, {"desc": "323", "fix": "324"}, {"messageId": "287", "fix": "325", "desc": "289"}, {"messageId": "290", "fix": "326", "desc": "292"}, {"desc": "327", "fix": "328"}, {"messageId": "287", "fix": "329", "desc": "289"}, {"messageId": "290", "fix": "330", "desc": "292"}, {"messageId": "287", "fix": "331", "desc": "289"}, {"messageId": "290", "fix": "332", "desc": "292"}, {"messageId": "287", "fix": "333", "desc": "289"}, {"messageId": "290", "fix": "334", "desc": "292"}, {"messageId": "287", "fix": "335", "desc": "289"}, {"messageId": "290", "fix": "336", "desc": "292"}, {"messageId": "287", "fix": "337", "desc": "289"}, {"messageId": "290", "fix": "338", "desc": "292"}, {"messageId": "287", "fix": "339", "desc": "289"}, {"messageId": "290", "fix": "340", "desc": "292"}, {"messageId": "287", "fix": "341", "desc": "289"}, {"messageId": "290", "fix": "342", "desc": "292"}, {"messageId": "287", "fix": "343", "desc": "289"}, {"messageId": "290", "fix": "344", "desc": "292"}, "suggestUnknown", {"range": "345", "text": "346"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "347", "text": "348"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "349", "text": "346"}, {"range": "350", "text": "348"}, {"range": "351", "text": "346"}, {"range": "352", "text": "348"}, {"range": "353", "text": "346"}, {"range": "354", "text": "348"}, {"range": "355", "text": "346"}, {"range": "356", "text": "348"}, {"range": "357", "text": "346"}, {"range": "358", "text": "348"}, {"range": "359", "text": "346"}, {"range": "360", "text": "348"}, {"range": "361", "text": "346"}, {"range": "362", "text": "348"}, {"range": "363", "text": "346"}, {"range": "364", "text": "348"}, {"range": "365", "text": "346"}, {"range": "366", "text": "348"}, {"range": "367", "text": "346"}, {"range": "368", "text": "348"}, {"range": "369", "text": "346"}, {"range": "370", "text": "348"}, {"range": "371", "text": "346"}, {"range": "372", "text": "348"}, {"range": "373", "text": "346"}, {"range": "374", "text": "348"}, {"range": "375", "text": "346"}, {"range": "376", "text": "348"}, {"range": "377", "text": "346"}, {"range": "378", "text": "348"}, "Update the dependencies array to be: [runTests]", {"range": "379", "text": "380"}, {"range": "381", "text": "346"}, {"range": "382", "text": "348"}, "Update the dependencies array to be: [togglePlay]", {"range": "383", "text": "384"}, {"range": "385", "text": "346"}, {"range": "386", "text": "348"}, {"range": "387", "text": "346"}, {"range": "388", "text": "348"}, {"range": "389", "text": "346"}, {"range": "390", "text": "348"}, {"range": "391", "text": "346"}, {"range": "392", "text": "348"}, {"range": "393", "text": "346"}, {"range": "394", "text": "348"}, {"range": "395", "text": "346"}, {"range": "396", "text": "348"}, {"range": "397", "text": "346"}, {"range": "398", "text": "348"}, {"range": "399", "text": "346"}, {"range": "400", "text": "348"}, [5665, 5668], "unknown", [5665, 5668], "never", [5834, 5837], [5834, 5837], [7721, 7724], [7721, 7724], [17054, 17057], [17054, 17057], [17223, 17226], [17223, 17226], [19801, 19804], [19801, 19804], [21661, 21664], [21661, 21664], [22783, 22786], [22783, 22786], [2806, 2809], [2806, 2809], [4915, 4918], [4915, 4918], [3720, 3723], [3720, 3723], [5494, 5497], [5494, 5497], [141, 144], [141, 144], [197, 200], [197, 200], [263, 266], [263, 266], [329, 332], [329, 332], [444, 446], "[runTests]", [203, 206], [203, 206], [2519, 2521], "[togglePlay]", [5043, 5046], [5043, 5046], [5353, 5356], [5353, 5356], [5443, 5446], [5443, 5446], [5859, 5862], [5859, 5862], [6415, 6418], [6415, 6418], [6710, 6713], [6710, 6713], [6795, 6798], [6795, 6798], [7196, 7199], [7196, 7199]]