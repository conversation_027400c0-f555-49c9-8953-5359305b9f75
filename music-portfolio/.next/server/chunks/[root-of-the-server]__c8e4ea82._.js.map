{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/api/proxy-image/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const imageUrl = searchParams.get('url')\n\n    if (!imageUrl) {\n      return NextResponse.json(\n        { error: 'URL parameter is required' },\n        { status: 400 }\n      )\n    }\n\n    // 验证URL是否来自B站\n    if (!imageUrl.includes('bilibili.com') && !imageUrl.includes('hdslb.com')) {\n      return NextResponse.json(\n        { error: 'Only Bilibili images are allowed' },\n        { status: 400 }\n      )\n    }\n\n    // 获取图片\n    const response = await fetch(imageUrl, {\n      headers: {\n        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',\n        'Referer': 'https://www.bilibili.com/',\n        'Accept': 'image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',\n        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',\n        'Accept-Encoding': 'gzip, deflate, br',\n        'Connection': 'keep-alive',\n        'Sec-Fetch-Dest': 'image',\n        'Sec-Fetch-Mode': 'no-cors',\n        'Sec-Fetch-Site': 'cross-site',\n        'Cache-Control': 'no-cache',\n        'Pragma': 'no-cache'\n      }\n    })\n\n    if (!response.ok) {\n      console.error(`Failed to fetch image: ${response.status} ${response.statusText}`)\n      console.error(`Image URL: ${imageUrl}`)\n      return NextResponse.json(\n        { error: `Failed to fetch image: ${response.status} ${response.statusText}` },\n        { status: response.status }\n      )\n    }\n\n    // 获取图片数据\n    const imageBuffer = await response.arrayBuffer()\n    const contentType = response.headers.get('content-type') || 'image/jpeg'\n\n    // 返回图片，设置适当的缓存头\n    return new NextResponse(imageBuffer, {\n      status: 200,\n      headers: {\n        'Content-Type': contentType,\n        'Cache-Control': 'public, max-age=86400', // 缓存24小时\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET',\n        'Access-Control-Allow-Headers': 'Content-Type',\n      },\n    })\n  } catch (error) {\n    console.error('Error proxying image:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,WAAW,aAAa,GAAG,CAAC;QAElC,IAAI,CAAC,UAAU;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA4B,GACrC;gBAAE,QAAQ;YAAI;QAElB;QAEA,cAAc;QACd,IAAI,CAAC,SAAS,QAAQ,CAAC,mBAAmB,CAAC,SAAS,QAAQ,CAAC,cAAc;YACzE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmC,GAC5C;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO;QACP,MAAM,WAAW,MAAM,MAAM,UAAU;YACrC,SAAS;gBACP,cAAc;gBACd,WAAW;gBACX,UAAU;gBACV,mBAAmB;gBACnB,mBAAmB;gBACnB,cAAc;gBACd,kBAAkB;gBAClB,kBAAkB;gBAClB,kBAAkB;gBAClB,iBAAiB;gBACjB,UAAU;YACZ;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;YAChF,QAAQ,KAAK,CAAC,CAAC,WAAW,EAAE,UAAU;YACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,CAAC,uBAAuB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;YAAC,GAC5E;gBAAE,QAAQ,SAAS,MAAM;YAAC;QAE9B;QAEA,SAAS;QACT,MAAM,cAAc,MAAM,SAAS,WAAW;QAC9C,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;QAE5D,gBAAgB;QAChB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,aAAa;YACnC,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB;gBACjB,+BAA+B;gBAC/B,gCAAgC;gBAChC,gCAAgC;YAClC;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}