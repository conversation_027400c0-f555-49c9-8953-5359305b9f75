{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/components/Navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navigation.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navigation.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiS,GAC9T,+DACA", "debugId": null}}, {"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/components/Navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navigation.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navigation.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6Q,GAC1S,2CACA", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/components/Footer.tsx"], "sourcesContent": ["const Footer = () => {\n  return (\n    <footer className=\"bg-black text-white py-8 mt-16\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center\">\n          <p className=\"text-gray-400 text-sm tracking-wide\">\n            © 2024 KIMAHALA. All rights reserved.\n          </p>\n          <div className=\"mt-4 flex justify-center space-x-6\">\n            <a\n              href=\"https://space.bilibili.com/27744192?spm_id_from=333.1007.0.0\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"text-gray-400 hover:text-red-400 transition-colors duration-200 tracking-wide\"\n            >\n              BILIBILI\n            </a>\n            <a\n              href=\"#\"\n              className=\"text-gray-400 hover:text-red-400 transition-colors duration-200 tracking-wide\"\n            >\n              WEIBO\n            </a>\n            <a\n              href=\"#\"\n              className=\"text-gray-400 hover:text-red-400 transition-colors duration-200 tracking-wide\"\n            >\n              EMAIL\n            </a>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n\nexport default Footer\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAsC;;;;;;kCAGnD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,QAAO;gCACP,KAAI;gCACJ,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport \"./globals.css\";\nimport Navigation from \"@/components/Navigation\";\nimport Footer from \"@/components/Footer\";\n\nexport const metadata: Metadata = {\n  title: \"KIMAHALA - Musician & Content Creator\",\n  description: \"KIMAHALA's official website featuring original music and creative video content\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"zh-CN\">\n      <body className=\"antialiased bg-gray-900 text-white min-h-screen font-sans\">\n        <Navigation />\n        <main className=\"pt-16\">\n          {children}\n        </main>\n        <Footer />\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YAAK,WAAU;;8BACd,8OAAC,gIAAA,CAAA,UAAU;;;;;8BACX,8OAAC;oBAAK,WAAU;8BACb;;;;;;8BAEH,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;;;;;;AAIf", "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}