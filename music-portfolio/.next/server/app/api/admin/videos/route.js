(()=>{var a={};a.id=630,a.ids=[630],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(a,b,c)=>{"use strict";function d(a){let b=a.headers.get("authorization");if(!b||!b.startsWith("Basic "))return{isAuthenticated:!1};try{let a=b.slice(6),[c,d]=Buffer.from(a,"base64").toString("ascii").split(":"),e=process.env.ADMIN_USERNAME||"admin",f=process.env.ADMIN_PASSWORD||"admin123";if(c===e&&d===f)return{isAuthenticated:!0,username:c};return{isAuthenticated:!1}}catch(a){return{isAuthenticated:!1}}}function e(){return new Response("Unauthorized",{status:401,headers:{"WWW-Authenticate":'Basic realm="Admin Area"'}})}c.d(b,{c:()=>d,o:()=>e})},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65002:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>F,patchFetch:()=>E,routeModule:()=>A,serverHooks:()=>D,workAsyncStorage:()=>B,workUnitAsyncStorage:()=>C});var d={};c.r(d),c.d(d,{DELETE:()=>z,GET:()=>x,POST:()=>y});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190),v=c(12909);let w=[{id:"1",title:"《夜空中最亮的星》创作过程分享",description:"分享这首歌的创作灵感和制作过程，从词曲创作到录音制作的完整流程。",bilibili_url:"https://www.bilibili.com/video/BV1234567890",bilibili_embed_id:"BV1234567890",thumbnail_url:"/thumbnails/video1.jpg",created_at:"2024-01-15",updated_at:"2024-01-15"},{id:"2",title:"吉他弹唱《时光倒流》",description:"用吉他弹唱这首关于回忆的歌曲，希望能唤起大家心中美好的回忆。",bilibili_url:"https://www.bilibili.com/video/BV2345678901",bilibili_embed_id:"BV2345678901",thumbnail_url:"/thumbnails/video2.jpg",created_at:"2024-01-10",updated_at:"2024-01-10"},{id:"3",title:"音乐制作软件使用教程",description:"分享我常用的音乐制作软件和技巧，适合音乐制作初学者观看学习。",bilibili_url:"https://www.bilibili.com/video/BV3456789012",bilibili_embed_id:"BV3456789012",thumbnail_url:"/thumbnails/video3.jpg",created_at:"2024-01-05",updated_at:"2024-01-05"}];async function x(a){return(0,v.c)(a).isAuthenticated?u.NextResponse.json({success:!0,data:w}):(0,v.o)()}async function y(a){if(!(0,v.c)(a).isAuthenticated)return(0,v.o)();try{let{title:b,description:c,bilibili_url:d,thumbnail_url:e}=await a.json();if(!b||!d)return u.NextResponse.json({error:"Title and bilibili_url are required"},{status:400});let f=function(a){let b=a.match(/BV[a-zA-Z0-9]+/);return b?b[0]:""}(d);if(!f)return u.NextResponse.json({error:"Invalid Bilibili URL. Please provide a valid BV link."},{status:400});let g={id:Date.now().toString(),title:b,description:c||"",bilibili_url:d,bilibili_embed_id:f,thumbnail_url:e||"",created_at:new Date().toISOString().split("T")[0],updated_at:new Date().toISOString().split("T")[0]};return w.push(g),u.NextResponse.json({success:!0,data:g})}catch(a){return u.NextResponse.json({error:"Invalid request body"},{status:400})}}async function z(a){if(!(0,v.c)(a).isAuthenticated)return(0,v.o)();try{let{searchParams:b}=new URL(a.url),c=b.get("id");if(!c)return u.NextResponse.json({error:"ID is required"},{status:400});let d=w.findIndex(a=>a.id===c);if(-1===d)return u.NextResponse.json({error:"Video not found"},{status:404});return w.splice(d,1),u.NextResponse.json({success:!0})}catch(a){return u.NextResponse.json({error:"Failed to delete video"},{status:500})}}let A=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/admin/videos/route",pathname:"/api/admin/videos",filename:"route",bundlePath:"app/api/admin/videos/route"},distDir:".next",projectDir:"",resolvedPagePath:"/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/api/admin/videos/route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:B,workUnitAsyncStorage:C,serverHooks:D}=A;function E(){return(0,g.patchFetch)({workAsyncStorage:B,workUnitAsyncStorage:C})}async function F(a,b,c){var d;let e="/api/admin/videos/route";"/index"===e&&(e="/");let g=await A.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:z,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(y.dynamicRoutes[E]||y.routes[D]);if(F&&!x){let a=!!y.routes[D],b=y.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||A.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===A.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>A.onRequestError(a,b,d,z)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>A.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await A.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},z),b}},l=await A.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await A.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},78335:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96487:()=>{}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,55],()=>b(b.s=65002));module.exports=c})();