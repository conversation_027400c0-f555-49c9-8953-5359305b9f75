# 马君个人主页功能说明

## 功能概述

本项目是为马君（KIMAHALA）打造的个人音乐作品集网站，包含以下三个主要功能模块：

### 1. Music页面功能修改 ✅

**原功能**: 展示音频播放器和音乐作品
**新功能**: 支持B站视频嵌入播放

**主要特性**:
- 支持B站视频iframe嵌入播放
- 视频缩略图展示
- 点击播放按钮可在页面内播放视频
- 支持跳转到B站观看完整视频
- 响应式设计，支持移动端

**技术实现**:
- 使用B站播放器API: `//player.bilibili.com/player.html?bvid=${embedId}&page=1&high_quality=1&danmaku=0`
- 自动从B站URL中提取视频ID (支持BV号、av号、b23.tv短链接)
- 视频数据结构包含: 标题、演唱者、描述、B站链接、缩略图等

### 2. 导航栏和Movie页面重构 ✅

**导航栏修改**:
- 将原有的"VIDEOS"导航项重命名为"MOVIES"
- 路由从 `/video` 改为 `/movie`

**Movie页面功能**:
- 电影信息展示，包含海报、名称、描述、评分、上映年份
- 按类型分类浏览：黑帮类、情感类、历史类、惊悚类、剧情类、动作类
- 支持外部观看链接（可选）
- 响应式卡片布局
- 评分星级显示
- 分类筛选功能

**电影数据结构**:
```typescript
interface Movie {
  id: string
  title: string           // 电影名称
  description: string     // 电影描述
  poster_url?: string     // 海报图片URL
  movie_link?: string     // 观看链接（可选）
  category: string        // 分类
  rating?: number         // 评分 (0-10)
  release_year?: number   // 上映年份
  created_at: string
  updated_at: string
}
```

### 3. 后台管理功能开发 ✅

**Music管理模块**:
- ✅ 添加B站视频：输入视频标题、演唱者、B站链接、缩略图URL、描述
- ✅ 自动验证B站链接有效性并提取视频ID
- ✅ 支持编辑和删除音乐视频
- ✅ 视频预览功能（显示缩略图和基本信息）
- ✅ 支持的B站URL格式：
  - `https://www.bilibili.com/video/BV...`
  - `https://www.bilibili.com/video/av...`
  - `https://b23.tv/...`

**Movie管理模块**:
- ✅ 添加电影信息：名称、描述、分类、评分、上映年份
- ✅ 上传电影海报图片
- ✅ 设置观看链接（可选）
- ✅ 选择或自定义电影分类
- ✅ 完整的增删改查功能
- ✅ 支持批量管理和筛选

**权限验证**:
- ✅ HTTP Basic Auth认证
- ✅ 环境变量配置用户名密码
- ✅ 所有管理操作都需要认证

**文件上传功能**:
- ✅ 支持图片上传 (JPEG, PNG, WebP)
- ✅ 文件大小限制 (5MB)
- ✅ 自动生成唯一文件名
- ✅ 分类存储 (posters/, thumbnails/)

## 技术栈

- **前端**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **后端**: Next.js API Routes
- **数据库**: Supabase (已配置但当前使用模拟数据)
- **UI组件**: Lucide React Icons
- **认证**: HTTP Basic Auth
- **文件上传**: Node.js fs/promises

## 环境配置

创建 `.env.local` 文件：

```env
# 管理员认证
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_secure_password

# Supabase配置 (可选)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## 使用说明

### 访问后台管理
1. 访问 `/admin` 页面
2. 浏览器会弹出认证对话框
3. 输入配置的用户名和密码
4. 成功登录后可以管理音乐视频和电影信息

### 添加音乐视频
1. 在后台管理的"音乐管理"标签页
2. 点击"添加音乐视频"
3. 填写视频信息，包括B站链接
4. 系统会自动验证链接并提取视频ID

### 添加电影信息
1. 在后台管理的"电影管理"标签页
2. 点击"添加电影"
3. 填写电影信息，选择分类
4. 可选择上传海报图片和设置观看链接

### 前台浏览
- **Music页面**: 浏览音乐视频，支持页面内播放和跳转B站
- **Movie页面**: 按分类浏览电影，查看详细信息
- **响应式设计**: 支持桌面端和移动端访问

## 部署说明

1. 确保所有依赖已安装: `npm install`
2. 配置环境变量
3. 构建项目: `npm run build`
4. 启动生产服务器: `npm start`

## 数据迁移

当前使用模拟数据，如需迁移到Supabase：
1. 在Supabase中创建对应的数据表
2. 修改API路由以使用Supabase客户端
3. 更新数据类型定义

## 安全注意事项

- 管理员密码应使用强密码
- 生产环境中应使用HTTPS
- 文件上传有大小和类型限制
- 所有管理操作都需要认证
