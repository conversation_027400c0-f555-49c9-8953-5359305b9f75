#!/bin/bash

# 设置错误处理
set -e
trap 'echo "❌ 脚本执行失败，行号: $LINENO"' ERR

echo "🚀 强力部署脚本 - 专为远程服务器设计"
echo "========================================"

# 函数：检查命令是否存在
check_command() {
    if ! command -v "$1" &> /dev/null; then
        echo "❌ $1 命令不存在"
        return 1
    fi
    return 0
}

# 函数：安全执行命令
safe_run() {
    echo "🔧 执行: $*"
    if ! "$@"; then
        echo "❌ 命令执行失败: $*"
        return 1
    fi
    return 0
}

# 检查基本环境
echo "📋 检查基本环境..."
check_command node || { echo "请先安装 Node.js"; exit 1; }
check_command npm || { echo "请先安装 npm"; exit 1; }

NODE_VERSION=$(node --version)
echo "✅ Node.js 版本: $NODE_VERSION"

# 检查 Node.js 版本是否满足要求
NODE_MAJOR=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
if [ "$NODE_MAJOR" -lt 14 ]; then
    echo "❌ Node.js 版本过低，需要 >= 14.0.0"
    exit 1
fi

# 设置 npm 配置（解决网络问题）
echo "🔧 配置 npm..."
npm config set registry https://registry.npmjs.org/
npm config set fetch-retries 5
npm config set fetch-retry-factor 2
npm config set fetch-retry-mintimeout 10000
npm config set fetch-retry-maxtimeout 60000

# 清理环境
echo "🧹 清理旧环境..."
safe_run rm -rf node_modules
safe_run rm -f package-lock.json
safe_run rm -rf .next

# 检查磁盘空间
echo "📊 检查磁盘空间..."
AVAILABLE=$(df . | tail -1 | awk '{print $4}')
if [ "$AVAILABLE" -lt 1048576 ]; then  # 小于 1GB
    echo "⚠️  磁盘空间不足，可能影响安装"
fi

# 安装依赖（分步骤，更稳定）
echo "📦 安装基础依赖..."
safe_run npm install --legacy-peer-deps --no-audit --no-fund

echo "📦 安装 CSS 处理依赖..."
safe_run npm install tailwindcss@latest postcss@latest autoprefixer@latest --save-dev --legacy-peer-deps

# 验证关键文件
echo "🔍 验证配置文件..."
required_files=("package.json" "tailwind.config.ts" "postcss.config.mjs")
for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 缺少文件: $file"
        exit 1
    fi
    echo "✅ $file 存在"
done

# 验证 globals.css
if [ ! -f "src/app/globals.css" ]; then
    echo "❌ 缺少 src/app/globals.css"
    exit 1
fi

# 检查 globals.css 内容
if ! grep -q "tailwindcss/base" src/app/globals.css; then
    echo "⚠️  globals.css 可能需要更新"
fi

# 创建环境配置文件
echo "🔧 检查环境配置..."
if [ ! -f ".env.local" ]; then
    echo "创建 .env.local 文件..."
    cat > .env.local << 'EOF'
# 管理员认证
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123

# Supabase配置 (可选 - 目前使用模拟数据)
# NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
# NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
EOF
    chmod 600 .env.local
    echo "✅ .env.local 已创建"
else
    echo "✅ .env.local 已存在"
fi

# 验证依赖安装
echo "🔍 验证依赖安装..."
critical_deps=("next" "react" "tailwindcss" "postcss" "autoprefixer")
for dep in "${critical_deps[@]}"; do
    if [ ! -d "node_modules/$dep" ]; then
        echo "❌ $dep 未正确安装"
        exit 1
    fi
    echo "✅ $dep 已安装"
done

# 尝试构建
echo "🏗️  开始构建..."
if safe_run npm run build; then
    echo "✅ 构建成功！"
else
    echo "❌ 构建失败"
    echo "🔧 尝试修复常见问题..."
    
    # 重新安装 Tailwind CSS
    echo "重新安装 Tailwind CSS..."
    npm uninstall tailwindcss postcss autoprefixer
    npm install tailwindcss@3.4.17 postcss@8.4.49 autoprefixer@10.4.20 --save-dev --legacy-peer-deps
    
    # 再次尝试构建
    if safe_run npm run build; then
        echo "✅ 修复后构建成功！"
    else
        echo "❌ 构建仍然失败，请检查错误日志"
        exit 1
    fi
fi

# 显示最终状态
echo ""
echo "🎉 部署完成！"
echo "========================================"
echo "📊 最终状态:"
echo "Node.js: $(node --version)"
echo "npm: $(npm --version)"
echo "项目依赖:"
npm list --depth=0 | grep -E "(next|react|tailwindcss|postcss|autoprefixer)" || true

echo ""
echo "🚀 启动命令:"
echo "npm start"
echo ""
echo "🔧 如果启动失败，尝试:"
echo "npm run dev"
