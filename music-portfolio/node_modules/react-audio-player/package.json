{"name": "react-audio-player", "version": "0.17.0", "description": "A simple React wrapper for the audio tag", "main": "dist/bundle.js", "types": "dist/index.d.ts", "scripts": {"test": "jest", "build": "webpack -p", "build:d.ts": "tsc", "watch": "webpack -p --watch", "prepare": "npm run build && npm run build:d.ts", "example": "webpack-dev-server --content-base example/ --config example/webpack.config.js --progress --colors"}, "repository": {"type": "git", "url": "https://github.com/justinmc/react-audio-player"}, "keywords": ["react", "audio", "player", "wrapper", "simple"], "author": "<PERSON>", "license": "MIT", "peerDependencies": {"react": ">=16", "react-dom": ">=16"}, "dependencies": {"prop-types": "^15.7.2"}, "devDependencies": {"@babel/core": "^7.9.0", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/preset-env": "^7.9.5", "@babel/preset-react": "^7.9.4", "@babel/preset-typescript": "^7.8.3", "@types/react": "^16.9.23", "babel-jest": "^25.4.0", "babel-loader": "^8.1.0", "eslint": "^5.16.0", "eslint-config-airbnb": "^17.1.1", "eslint-loader": "^2.2.1", "eslint-plugin-import": "^2.20.2", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-react": "^7.19.0", "file-loader": "^3.0.1", "jest": "^25.4.0", "react": "^16.13.1", "react-addons-test-utils": "^15.6.2", "react-dom": "^16.13.1", "react-hot-loader": "^4.12.20", "react-test-renderer": "^16.13.1", "typescript": "^3.8.3", "webpack": "^4.42.1", "webpack-cli": "^3.3.11", "webpack-dev-server": "^3.11.2"}}