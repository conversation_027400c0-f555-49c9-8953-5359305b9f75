# 🚀 远程服务器部署指南

## 🎯 快速部署（推荐）

### 方法一：一键部署
```bash
chmod +x super-simple-deploy.sh
./super-simple-deploy.sh
```

### 方法二：如果脚本没反应
```bash
bash super-simple-deploy.sh
```

### 方法三：完全手动（最可靠）
```bash
# 1. 清理环境
rm -rf node_modules package-lock.json .next

# 2. 安装依赖
npm install next@15.4.6 react@19.1.0 react-dom@19.1.0
npm install @supabase/supabase-js lucide-react react-audio-player
npm install @types/node @types/react @types/react-dom typescript --save-dev
npm install tailwindcss postcss autoprefixer --save-dev
npm install eslint eslint-config-next --save-dev

# 3. 构建
npm run build

# 4. 启动
npm start
```

## 🔧 如果遇到问题

### 1. 脚本没有执行权限
```bash
chmod +x *.sh
```

### 2. 网络问题（中国服务器）
```bash
npm config set registry https://registry.npm.taobao.org/
```

### 3. 内存不足
```bash
export NODE_OPTIONS="--max-old-space-size=2048"
```

### 4. 权限问题
```bash
sudo chown -R $USER:$USER .
```

## 📋 环境要求

- Node.js >= 14.17.0
- npm >= 6.0.0
- 至少 2GB 可用磁盘空间
- 至少 1GB 可用内存

## 🆘 紧急联系

如果所有方法都不工作，请提供以下信息：

```bash
# 运行这些命令并发送输出
uname -a
node --version
npm --version
df -h
free -h
ls -la
```

## ✅ 成功标志

看到以下输出表示部署成功：
```
✓ Compiled successfully
✓ Generating static pages
✓ Finalizing page optimization
```

然后运行：
```bash
npm start
```

项目将在 http://localhost:3000 启动。
