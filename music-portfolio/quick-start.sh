#!/bin/bash

echo "🚀 快速启动脚本"
echo "================"

echo "1. 修复 TypeScript 错误已完成 ✅"
echo "   问题: admin/page.tsx 中缺少 video_file_path 字段"
echo "   解决: 已添加缺失的字段"

echo ""
echo "2. 重新构建项目..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ 构建成功！"
    
    # 停止可能运行的旧进程
    if [ -f "app.pid" ]; then
        old_pid=$(cat app.pid)
        if ps -p $old_pid > /dev/null; then
            echo "3. 停止旧进程 (PID: $old_pid)..."
            kill $old_pid
            sleep 2
        fi
        rm app.pid
    fi
    
    echo "4. 启动应用..."
    nohup npm start > app.log 2>&1 &
    echo $! > app.pid
    
    sleep 5
    
    # 检查应用是否成功启动
    if ps -p $(cat app.pid) > /dev/null; then
        echo ""
        echo "🎉 应用启动成功！"
        echo "================================="
        echo "📊 部署信息:"
        echo "- 状态: ✅ 运行中"
        echo "- PID: $(cat app.pid)"
        echo "- 端口: 3000"
        echo "- 本地访问: http://localhost:3000"
        echo "- 外部访问: http://$(hostname -I | awk '{print $1}'):3000"
        echo ""
        echo "🎵 功能确认:"
        echo "- ✅ 音乐作品展示"
        echo "- ✅ 视频内容管理"
        echo "- ✅ 管理员后台"
        echo "- ✅ Tailwind CSS 样式"
        echo "- ✅ Lucide React 图标"
        echo "- ✅ Supabase 数据库"
        echo "- ✅ 音频播放器"
        echo ""
        echo "🛠️ 管理命令:"
        echo "- 查看日志: tail -f app.log"
        echo "- 停止应用: kill $(cat app.pid)"
        echo "- 重启应用: ./quick-start.sh"
        echo ""
        
        # 测试应用响应
        echo "🔍 测试应用响应..."
        sleep 2
        if curl -s http://localhost:3000 > /dev/null; then
            echo "✅ 应用响应正常！"
        else
            echo "⚠️ 应用可能还在启动中，请稍等片刻"
        fi
        
        echo ""
        echo "📋 最近启动日志:"
        tail -n 8 app.log
        
        echo ""
        echo "🎊 恭喜！Kimahala 音乐作品集部署完成！"
        echo "   你的音乐作品集现在可以正常访问了！"
        
    else
        echo "❌ 应用启动失败"
        echo ""
        echo "📋 错误日志:"
        cat app.log
        echo ""
        echo "🔧 可能的解决方案:"
        echo "1. 检查端口是否被占用: netstat -tlnp | grep :3000"
        echo "2. 手动启动: npm start"
        echo "3. 查看详细日志: cat app.log"
        exit 1
    fi
    
else
    echo "❌ 构建失败"
    echo ""
    echo "📋 请检查构建错误并修复后重试"
    echo "🔧 调试命令:"
    echo "- 详细构建: npm run build --verbose"
    echo "- 检查语法: npm run lint"
    exit 1
fi
