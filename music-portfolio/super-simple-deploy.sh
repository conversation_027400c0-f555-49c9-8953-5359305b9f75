#!/bin/bash

echo "🚀 超级简单部署脚本 - 专为远程服务器设计"
echo "=========================================="

# 基本检查
echo "检查 Node.js..."
if ! node --version; then
    echo "❌ 请先安装 Node.js"
    exit 1
fi

echo "检查 npm..."
if ! npm --version; then
    echo "❌ 请先安装 npm"
    exit 1
fi

# 强制清理
echo "清理旧文件..."
rm -rf node_modules
rm -f package-lock.json
rm -rf .next

# 设置 npm
echo "配置 npm..."
npm config set registry https://registry.npmjs.org/

# 安装依赖 - 分步骤进行
echo "安装基础依赖..."
npm install next@15.4.6 react@19.1.0 react-dom@19.1.0 --save

echo "安装其他依赖..."
npm install @supabase/supabase-js lucide-react react-audio-player --save

echo "安装开发依赖..."
npm install @types/node @types/react @types/react-dom typescript --save-dev

echo "安装 CSS 依赖..."
npm install tailwindcss postcss autoprefixer --save-dev

echo "安装 ESLint..."
npm install eslint eslint-config-next --save-dev

# 创建环境配置文件
echo "创建环境配置..."
if [ ! -f ".env.local" ]; then
    cat > .env.local << 'EOF'
# 管理员认证
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123

# Supabase配置 (可选 - 目前使用模拟数据)
# NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
# NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
EOF
    chmod 600 .env.local
    echo "✅ .env.local 已创建"
else
    echo "✅ .env.local 已存在"
fi

# 验证安装
echo "验证关键依赖..."
if [ ! -d "node_modules/next" ]; then
    echo "❌ Next.js 安装失败"
    exit 1
fi

if [ ! -d "node_modules/tailwindcss" ]; then
    echo "❌ Tailwind CSS 安装失败"
    exit 1
fi

echo "✅ 依赖安装完成"

# 构建
echo "开始构建..."
if npm run build; then
    echo "✅ 构建成功！"
    echo ""
    echo "🚀 启动命令："
    echo "npm start"
    echo ""
    echo "🔧 开发模式："
    echo "npm run dev"
else
    echo "❌ 构建失败"
    echo "请检查错误信息"
    exit 1
fi
