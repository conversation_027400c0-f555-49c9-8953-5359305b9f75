#!/bin/bash

echo "🎨 修复 Tailwind CSS 配置问题"
echo "================================"

echo "1. 分析问题..."
echo "   问题: globals.css 使用 Tailwind v4 语法，但依赖是 v3"
echo "   解决: 更新到兼容的配置"

echo "2. 备份现有文件..."
cp src/app/globals.css src/app/globals.css.backup
cp postcss.config.mjs postcss.config.mjs.backup

echo "3. 检查当前 Tailwind 版本..."
if [ -d "node_modules/tailwindcss" ]; then
    tailwind_version=$(npm list tailwindcss --depth=0 2>/dev/null | grep tailwindcss | cut -d'@' -f2)
    echo "   当前 Tailwind 版本: $tailwind_version"
else
    echo "   Tailwind CSS 未安装"
fi

echo "4. 修复 globals.css（兼容 Tailwind v3）..."
cat > src/app/globals.css << 'EOF'
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #111827;
  --foreground: #f9fafb;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1f2937;
}

::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* 音频播放器样式 */
.audio-player {
  background: #1f2937;
  border-radius: 8px;
  padding: 1rem;
}

/* 视频容器样式 */
.video-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

/* 文本截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 视频播放器控制条样式 */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #ef4444;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
}

.slider::-moz-range-thumb {
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #ef4444;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
}

.slider::-webkit-slider-track {
  background: #4b5563;
  height: 4px;
  border-radius: 2px;
}

.slider::-moz-range-track {
  background: #4b5563;
  height: 4px;
  border-radius: 2px;
  border: none;
}
EOF

echo "5. 修复 PostCSS 配置（兼容 Tailwind v3）..."
cat > postcss.config.mjs << 'EOF'
const config = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
};

export default config;
EOF

echo "6. 创建 Tailwind 配置文件..."
cat > tailwind.config.js << 'EOF'
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        background: 'var(--background)',
        foreground: 'var(--foreground)',
      },
    },
  },
  plugins: [],
}
EOF

echo "7. 确保安装正确的 Tailwind 版本..."
echo "   卸载可能冲突的版本..."
npm uninstall tailwindcss @tailwindcss/postcss 2>/dev/null || true

echo "   安装 Tailwind CSS v3 和相关依赖..."
npm install tailwindcss@^3.3.0 autoprefixer@^10.4.0 postcss@^8.4.0 --save-dev --legacy-peer-deps

if [ $? -ne 0 ]; then
    echo "❌ Tailwind 安装失败，尝试强制安装..."
    npm install tailwindcss@^3.3.0 autoprefixer@^10.4.0 postcss@^8.4.0 --save-dev --force
fi

echo "8. 验证安装..."
if [ -d "node_modules/tailwindcss" ]; then
    echo "✅ Tailwind CSS 安装成功"
    new_version=$(npm list tailwindcss --depth=0 2>/dev/null | grep tailwindcss | cut -d'@' -f2)
    echo "   新版本: $new_version"
else
    echo "❌ Tailwind CSS 安装失败"
    exit 1
fi

echo "9. 清理构建缓存..."
rm -rf .next

echo "10. 测试构建..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ Tailwind CSS 修复成功！"
    echo ""
    echo "🎨 修复内容:"
    echo "- ✅ globals.css: v4 语法 → v3 语法"
    echo "- ✅ PostCSS: 更新配置"
    echo "- ✅ Tailwind: 安装 v3.3.0"
    echo "- ✅ 配置文件: 创建 tailwind.config.js"
    echo ""
    echo "🚀 现在可以启动应用了:"
    echo "   npm start"
    
else
    echo "❌ 构建仍然失败"
    echo ""
    echo "🔄 恢复备份文件..."
    mv src/app/globals.css.backup src/app/globals.css 2>/dev/null || true
    mv postcss.config.mjs.backup postcss.config.mjs 2>/dev/null || true
    rm tailwind.config.js 2>/dev/null || true
    
    echo "📋 请检查以下内容:"
    echo "1. Node.js 版本: $(node --version)"
    echo "2. npm 版本: $(npm --version)"
    echo "3. 项目依赖: npm list --depth=0"
    
    exit 1
fi
