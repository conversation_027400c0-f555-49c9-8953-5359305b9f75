#!/bin/bash

echo "💪 强力修复脚本 - 解决文件系统锁定问题"
echo "========================================="

echo "1. 停止所有可能的 Node.js 进程..."
pkill -f node 2>/dev/null || true
pkill -f npm 2>/dev/null || true
pkill -f next 2>/dev/null || true
sleep 2

echo "2. 强制清理 node_modules..."
echo "   这可能需要几分钟时间..."

# 尝试正常删除
rm -rf node_modules 2>/dev/null || true

# 如果还有残留，强制删除
if [ -d "node_modules" ]; then
    echo "   正常删除失败，使用强制方法..."
    
    # 修改权限
    chmod -R 777 node_modules 2>/dev/null || true
    
    # 强制删除
    rm -rf node_modules
    
    # 如果还是失败，逐个删除
    if [ -d "node_modules" ]; then
        echo "   逐个删除顽固文件..."
        find node_modules -type f -exec rm -f {} \; 2>/dev/null || true
        find node_modules -type d -exec rmdir {} \; 2>/dev/null || true
        rm -rf node_modules 2>/dev/null || true
    fi
fi

echo "3. 清理所有缓存和锁文件..."
rm -rf package-lock.json
rm -rf .next
rm -rf .npm
rm -rf ~/.npm/_cacache 2>/dev/null || true
npm cache clean --force 2>/dev/null || true

echo "4. 确保使用正确的 package.json..."
if [ -f "package.legacy.json" ]; then
    cp package.legacy.json package.json
    echo "✅ 已恢复原始 package.json"
else
    echo "⚠️ 未找到 package.legacy.json"
fi

echo "5. 修复 Tailwind CSS 配置文件..."

# 修复 globals.css
echo "   修复 globals.css..."
cat > src/app/globals.css << 'EOF'
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #111827;
  --foreground: #f9fafb;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1f2937;
}

::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* 音频播放器样式 */
.audio-player {
  background: #1f2937;
  border-radius: 8px;
  padding: 1rem;
}

/* 视频容器样式 */
.video-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%;
}

.video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

/* 文本截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 视频播放器控制条样式 */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #ef4444;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
}

.slider::-moz-range-thumb {
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #ef4444;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
}

.slider::-webkit-slider-track {
  background: #4b5563;
  height: 4px;
  border-radius: 2px;
}

.slider::-moz-range-track {
  background: #4b5563;
  height: 4px;
  border-radius: 2px;
  border: none;
}
EOF

# 修复 PostCSS 配置
echo "   修复 PostCSS 配置..."
cat > postcss.config.js << 'EOF'
module.exports = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}
EOF

# 创建 Tailwind 配置
echo "   创建 Tailwind 配置..."
cat > tailwind.config.js << 'EOF'
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        background: 'var(--background)',
        foreground: 'var(--foreground)',
      },
    },
  },
  plugins: [],
}
EOF

echo "6. 修复 TypeScript 配置..."
sed -i 's/"moduleResolution": "bundler"/"moduleResolution": "node"/' tsconfig.json

echo "7. 修复 Next.js 配置..."
rm -f next.config.ts next.config.mjs
cat > next.config.js << 'EOF'
/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  images: {
    domains: ['localhost'],
  },
}

module.exports = nextConfig
EOF

echo "8. 分步安装依赖（避免冲突）..."

echo "   第1步: 安装核心依赖..."
npm install next@13.5.6 react@18.2.0 react-dom@18.2.0 --no-package-lock

echo "   第2步: 安装 TypeScript 相关..."
npm install typescript@^4.9.0 @types/node@^18 @types/react@^18 @types/react-dom@^18 --save-dev --no-package-lock

echo "   第3步: 安装 Tailwind CSS..."
npm install tailwindcss@^3.3.0 autoprefixer@^10.4.0 postcss@^8.4.0 --save-dev --no-package-lock

echo "   第4步: 安装其他依赖..."
npm install lucide-react@^0.263.0 --no-package-lock
npm install @supabase/supabase-js@^2.39.0 --no-package-lock
npm install react-audio-player@^0.17.0 --no-package-lock

echo "   第5步: 安装开发依赖..."
npm install eslint@^8 eslint-config-next@13.5.6 --save-dev --no-package-lock

echo "9. 生成 package-lock.json..."
npm install --package-lock-only

echo "10. 验证安装..."
echo "📦 已安装的关键依赖:"
npm list --depth=0 | grep -E "(next|react|tailwindcss|lucide-react|supabase)" || echo "依赖列表获取失败，但继续尝试构建..."

echo "11. 测试构建..."
npm run build

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 强力修复成功！"
    echo "================================="
    echo "✅ 所有依赖已正确安装"
    echo "✅ Tailwind CSS 配置已修复"
    echo "✅ TypeScript 配置已修复"
    echo "✅ Next.js 配置已修复"
    echo "✅ 项目构建成功"
    echo ""
    echo "🚀 启动应用..."
    
    # 启动应用
    nohup npm start > app.log 2>&1 &
    echo $! > app.pid
    
    sleep 5
    
    if ps -p $(cat app.pid) > /dev/null; then
        echo "✅ 应用启动成功！"
        echo ""
        echo "📊 部署信息:"
        echo "- PID: $(cat app.pid)"
        echo "- 访问地址: http://$(hostname -I | awk '{print $1}'):3000"
        echo "- 日志文件: app.log"
        echo "- 停止命令: kill $(cat app.pid)"
        echo ""
        echo "🎊 恭喜！你的音乐作品集已成功部署！"
        
        # 显示最近日志
        echo "📋 启动日志:"
        tail -n 5 app.log
        
    else
        echo "❌ 应用启动失败，查看日志: cat app.log"
    fi
    
else
    echo ""
    echo "❌ 构建失败"
    echo "🔍 调试信息:"
    echo "- Node.js: $(node --version)"
    echo "- npm: $(npm --version)"
    echo "- 工作目录: $(pwd)"
    echo ""
    echo "📋 请尝试手动调试:"
    echo "1. npm run build --verbose"
    echo "2. 检查错误日志"
    echo "3. 如需帮助，提供具体错误信息"
fi
