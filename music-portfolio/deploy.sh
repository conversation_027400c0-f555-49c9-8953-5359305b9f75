#!/bin/bash

# 音乐作品集部署脚本
echo "开始部署音乐作品集..."

# 检查 Node.js 版本
echo "检查 Node.js 版本..."
node_version=$(node --version)
echo "当前 Node.js 版本: $node_version"

# 提取版本号进行比较
node_major_version=$(echo $node_version | cut -d'.' -f1 | sed 's/v//')

if [ "$node_major_version" -lt 14 ]; then
    echo "警告: Node.js 版本过低 ($node_version)，项目推荐使用 Node.js 14+。"
    echo "尝试使用兼容版本的依赖..."

    if [ -f "package.legacy.json" ]; then
        echo "使用兼容版本的 package.json..."
        cp package.json package.json.backup
        cp package.legacy.json package.json
    else
        echo "错误: 找不到兼容版本的配置文件。"
        echo "请升级 Node.js 到 14+ 版本，或联系管理员。"
        echo ""
        echo "升级命令示例："
        echo "curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -"
        echo "sudo apt-get install -y nodejs"
        exit 1
    fi
fi

# 清理旧的构建文件
echo "清理旧的构建文件..."
rm -rf .next
rm -rf node_modules

# 安装依赖
echo "安装依赖..."
npm install

# 构建项目
echo "构建生产版本..."
npm run build

# 检查构建是否成功
if [ $? -eq 0 ]; then
    echo "构建成功！"
    
    # 启动应用
    echo "启动应用..."
    
    # 创建日志目录
    mkdir -p logs

    # 如果已安装 PM2，使用 PM2 启动
    if command -v pm2 &> /dev/null; then
        echo "使用 PM2 启动应用..."
        pm2 delete music-portfolio 2>/dev/null || true

        # 使用 ecosystem.config.js 配置文件启动
        if [ -f "ecosystem.config.js" ]; then
            pm2 start ecosystem.config.js
        else
            pm2 start npm --name "music-portfolio" -- start
        fi

        pm2 save
        echo "应用已通过 PM2 启动"
        echo "使用 'pm2 logs music-portfolio' 查看日志"
        echo "使用 'pm2 status' 查看状态"
        echo "使用 'pm2 monit' 查看监控面板"
    else
        echo "PM2 未安装，使用 npm start 启动..."
        echo "建议安装 PM2: npm install -g pm2"
        npm start
    fi
else
    echo "构建失败！请检查错误信息。"

    # 如果使用了兼容版本但仍然失败，恢复原始配置
    if [ -f "package.json.backup" ]; then
        echo "恢复原始 package.json..."
        mv package.json.backup package.json
    fi

    exit 1
fi

# 清理备份文件
if [ -f "package.json.backup" ]; then
    rm package.json.backup
fi

echo "部署完成！"
echo ""
echo "访问地址: http://localhost:3000"
echo "管理后台: http://localhost:3000/admin"
