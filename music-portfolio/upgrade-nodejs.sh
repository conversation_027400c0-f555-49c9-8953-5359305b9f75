#!/bin/bash

echo "Node.js 升级脚本"
echo "================"

# 检查当前版本
current_version=$(node --version 2>/dev/null || echo "未安装")
echo "当前 Node.js 版本: $current_version"

# 检查操作系统
if [ -f /etc/os-release ]; then
    . /etc/os-release
    OS=$NAME
    echo "操作系统: $OS"
else
    echo "无法检测操作系统"
    exit 1
fi

echo ""
echo "开始升级 Node.js 到最新 LTS 版本..."

# Ubuntu/Debian 系统
if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
    echo "检测到 Ubuntu/Debian 系统"
    
    # 更新包列表
    sudo apt-get update
    
    # 安装 curl（如果未安装）
    sudo apt-get install -y curl
    
    # 添加 NodeSource 仓库
    curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
    
    # 安装 Node.js
    sudo apt-get install -y nodejs
    
# CentOS/RHEL/Fedora 系统
elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]] || [[ "$OS" == *"Fedora"* ]]; then
    echo "检测到 CentOS/RHEL/Fedora 系统"
    
    # 安装 curl（如果未安装）
    sudo yum install -y curl || sudo dnf install -y curl
    
    # 添加 NodeSource 仓库
    curl -fsSL https://rpm.nodesource.com/setup_20.x | sudo bash -
    
    # 安装 Node.js
    sudo yum install -y nodejs || sudo dnf install -y nodejs

else
    echo "不支持的操作系统: $OS"
    echo "请手动安装 Node.js 20+ 版本"
    echo "访问: https://nodejs.org/en/download/"
    exit 1
fi

# 验证安装
echo ""
echo "验证安装结果..."
new_version=$(node --version)
npm_version=$(npm --version)

echo "新的 Node.js 版本: $new_version"
echo "npm 版本: $npm_version"

# 检查版本是否满足要求
node_major=$(echo $new_version | cut -d'.' -f1 | sed 's/v//')
if [ "$node_major" -ge 14 ]; then
    echo "✅ Node.js 升级成功！版本满足要求。"
    echo ""
    echo "现在可以运行部署脚本："
    echo "./deploy.sh"
else
    echo "❌ Node.js 版本仍然过低，请检查安装过程。"
    exit 1
fi
