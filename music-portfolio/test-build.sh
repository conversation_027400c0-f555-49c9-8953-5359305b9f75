#!/bin/bash

echo "🧪 测试构建脚本"
echo "================"

echo "1. 检查修复状态..."
echo "✅ API 路由类型错误已修复"
echo "✅ React hooks 导入已修复"
echo "✅ iframe 属性问题已修复"
echo "✅ find() 方法类型已修复"
echo "✅ Buffer 类型转换已修复"
echo "✅ useRef 初始值已修复"

echo ""
echo "2. 清理缓存..."
rm -rf .next
rm -rf node_modules/.cache 2>/dev/null || true

echo "3. 运行 TypeScript 检查..."
npx tsc --noEmit

if [ $? -eq 0 ]; then
    echo "✅ TypeScript 检查通过"
    
    echo ""
    echo "4. 运行构建..."
    npm run build
    
    if [ $? -eq 0 ]; then
        echo ""
        echo "🎉 构建成功！所有错误已修复！"
        echo "================================="
        echo ""
        echo "📋 修复总结:"
        echo "✅ src/app/api/video/[...path]/route.ts - Next.js 15 参数类型"
        echo "✅ src/app/video/page.tsx - useEffect 导入"
        echo "✅ src/app/music/page.tsx - iframe border 属性"
        echo "✅ src/app/video/page.tsx - iframe border 属性"
        echo "✅ src/app/admin/page.tsx - video_file_path 字段"
        echo "✅ src/app/api/admin/movies/route.ts - find() 类型"
        echo "✅ src/app/api/admin/music/route.ts - find() 类型"
        echo "✅ src/app/api/video/[...path]/route.ts - Buffer 类型转换"
        echo "✅ src/components/VideoPlayer.tsx - useRef 初始值"
        echo ""
        echo "🚀 项目已准备好部署到远程服务器！"
        echo ""
        echo "📝 下一步:"
        echo "1. 将修复推送到远程服务器"
        echo "2. 在远程服务器运行: ./final-start.sh"
        echo ""
        echo "🎊 恭喜！所有构建错误已解决！"
        
    else
        echo ""
        echo "❌ 构建失败 - 可能还有其他错误"
        echo "请检查构建输出中的具体错误信息"
        exit 1
    fi
    
else
    echo ""
    echo "❌ TypeScript 检查失败"
    echo "请检查 TypeScript 错误并修复"
    exit 1
fi
