import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// 数据库表结构类型定义
export interface MusicVideo {
  id: string
  title: string
  artist: string
  description?: string
  bilibili_url: string
  bilibili_embed_id: string
  thumbnail_url?: string
  created_at: string
  updated_at: string
}

export interface Movie {
  id: string
  title: string
  description: string
  poster_url?: string
  movie_link?: string
  video_file_path?: string  // 新增：本地视频文件路径
  category: string
  rating?: number
  release_year?: number
  created_at: string
  updated_at: string
}

export interface VideoItem {
  id: string
  title: string
  description?: string
  bilibili_url: string
  bilibili_embed_id: string
  thumbnail_url?: string
  created_at: string
  updated_at: string
}

export interface ProfileInfo {
  id: string
  name: string
  bio: string
  avatar_url?: string
  social_links?: {
    bilibili?: string
    weibo?: string
    email?: string
  }
  updated_at: string
}
