import { NextRequest } from 'next/server'

export interface AdminSession {
  isAuthenticated: boolean
  username?: string
}

export function checkAdminAuth(request: NextRequest): AdminSession {
  const authHeader = request.headers.get('authorization')
  
  if (!authHeader || !authHeader.startsWith('Basic ')) {
    return { isAuthenticated: false }
  }

  try {
    const base64Credentials = authHeader.slice(6)
    const credentials = Buffer.from(base64Credentials, 'base64').toString('ascii')
    const [username, password] = credentials.split(':')

    const adminUsername = process.env.ADMIN_USERNAME || 'admin'
    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123'

    if (username === adminUsername && password === adminPassword) {
      return { isAuthenticated: true, username }
    }

    return { isAuthenticated: false }
  } catch (error) {
    return { isAuthenticated: false }
  }
}

export function requireAuth() {
  return new Response('Unauthorized', {
    status: 401,
    headers: {
      'WWW-Authenticate': 'Basic realm="Admin Area"',
    },
  })
}
