import React from 'react';

export default function HomePage() {
  return (
    <div style={{ 
      minHeight: '100vh', 
      padding: '2rem',
      fontFamily: 'system-ui, -apple-system, sans-serif',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: 'white'
    }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        {/* Header */}
        <header style={{ textAlign: 'center', marginBottom: '3rem' }}>
          <h1 style={{ 
            fontSize: '3rem', 
            marginBottom: '1rem',
            textShadow: '2px 2px 4px rgba(0,0,0,0.3)'
          }}>
            🎵 Kimahala 音乐作品集
          </h1>
          <p style={{ fontSize: '1.2rem', opacity: 0.9 }}>
            欢迎来到我的音乐世界
          </p>
        </header>

        {/* Navigation */}
        <nav style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          gap: '2rem',
          marginBottom: '3rem'
        }}>
          <a href="#works" style={{ 
            color: 'white', 
            textDecoration: 'none',
            padding: '0.5rem 1rem',
            border: '2px solid white',
            borderRadius: '25px',
            transition: 'all 0.3s ease'
          }}>
            作品展示
          </a>
          <a href="#about" style={{ 
            color: 'white', 
            textDecoration: 'none',
            padding: '0.5rem 1rem',
            border: '2px solid white',
            borderRadius: '25px',
            transition: 'all 0.3s ease'
          }}>
            关于我
          </a>
          <a href="#contact" style={{ 
            color: 'white', 
            textDecoration: 'none',
            padding: '0.5rem 1rem',
            border: '2px solid white',
            borderRadius: '25px',
            transition: 'all 0.3s ease'
          }}>
            联系方式
          </a>
        </nav>

        {/* Works Section */}
        <section id="works" style={{ marginBottom: '3rem' }}>
          <h2 style={{ 
            fontSize: '2rem', 
            marginBottom: '2rem',
            textAlign: 'center'
          }}>
            🎼 我的作品
          </h2>
          
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '2rem'
          }}>
            {/* Sample Music Cards */}
            {[
              { title: '夜空中最亮的星', genre: '流行', duration: '4:32' },
              { title: '雨后彩虹', genre: '民谣', duration: '3:45' },
              { title: '城市节拍', genre: '电子', duration: '5:12' },
              { title: '温柔的风', genre: '轻音乐', duration: '3:28' }
            ].map((song, index) => (
              <div key={index} style={{
                background: 'rgba(255,255,255,0.1)',
                padding: '1.5rem',
                borderRadius: '15px',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255,255,255,0.2)'
              }}>
                <h3 style={{ marginBottom: '0.5rem' }}>{song.title}</h3>
                <p style={{ opacity: 0.8, marginBottom: '1rem' }}>
                  类型: {song.genre} | 时长: {song.duration}
                </p>
                <button style={{
                  background: 'rgba(255,255,255,0.2)',
                  border: 'none',
                  color: 'white',
                  padding: '0.5rem 1rem',
                  borderRadius: '20px',
                  cursor: 'pointer'
                }}>
                  ▶️ 播放
                </button>
              </div>
            ))}
          </div>
        </section>

        {/* About Section */}
        <section id="about" style={{ 
          marginBottom: '3rem',
          textAlign: 'center'
        }}>
          <h2 style={{ 
            fontSize: '2rem', 
            marginBottom: '2rem'
          }}>
            🎤 关于我
          </h2>
          <div style={{
            background: 'rgba(255,255,255,0.1)',
            padding: '2rem',
            borderRadius: '15px',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255,255,255,0.2)',
            maxWidth: '600px',
            margin: '0 auto'
          }}>
            <p style={{ lineHeight: '1.6', marginBottom: '1rem' }}>
              我是 Kimahala，一位热爱音乐创作的独立音乐人。
            </p>
            <p style={{ lineHeight: '1.6', marginBottom: '1rem' }}>
              专注于流行、民谣、电子等多种风格的音乐创作，
              希望通过音乐传递情感，连接人心。
            </p>
            <p style={{ lineHeight: '1.6' }}>
              每一首歌都是我内心世界的真实写照。
            </p>
          </div>
        </section>

        {/* Contact Section */}
        <section id="contact" style={{ textAlign: 'center' }}>
          <h2 style={{ 
            fontSize: '2rem', 
            marginBottom: '2rem'
          }}>
            📧 联系我
          </h2>
          <div style={{
            background: 'rgba(255,255,255,0.1)',
            padding: '2rem',
            borderRadius: '15px',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255,255,255,0.2)',
            maxWidth: '400px',
            margin: '0 auto'
          }}>
            <p style={{ marginBottom: '1rem' }}>
              📧 邮箱: <EMAIL>
            </p>
            <p style={{ marginBottom: '1rem' }}>
              🎵 微信: kimahala_music
            </p>
            <p>
              🎸 QQ: 123456789
            </p>
          </div>
        </section>

        {/* Footer */}
        <footer style={{ 
          textAlign: 'center', 
          marginTop: '3rem',
          padding: '2rem 0',
          borderTop: '1px solid rgba(255,255,255,0.2)'
        }}>
          <p style={{ opacity: 0.7 }}>
            © 2024 Kimahala Music Portfolio. 用音乐点亮生活 ✨
          </p>
        </footer>
      </div>
    </div>
  );
}
