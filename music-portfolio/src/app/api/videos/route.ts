import { NextResponse } from 'next/server'

// 这里应该从数据库获取数据，现在使用模拟数据
const videos = [
  {
    id: '1',
    title: '《夜空中最亮的星》创作过程分享',
    description: '分享这首歌的创作灵感和制作过程，从词曲创作到录音制作的完整流程。',
    bilibili_url: 'https://www.bilibili.com/video/BV1234567890',
    bilibili_embed_id: 'BV1234567890',
    thumbnail_url: '/thumbnails/video1.jpg',
    created_at: '2024-01-15',
  },
  {
    id: '2',
    title: '吉他弹唱《时光倒流》',
    description: '用吉他弹唱这首关于回忆的歌曲，希望能唤起大家心中美好的回忆。',
    bilibili_url: 'https://www.bilibili.com/video/BV2345678901',
    bilibili_embed_id: 'BV2345678901',
    thumbnail_url: '/thumbnails/video2.jpg',
    created_at: '2024-01-10',
  },
  {
    id: '3',
    title: '音乐制作软件使用教程',
    description: '分享我常用的音乐制作软件和技巧，适合音乐制作初学者观看学习。',
    bilibili_url: 'https://www.bilibili.com/video/BV3456789012',
    bilibili_embed_id: 'BV3456789012',
    thumbnail_url: '/thumbnails/video3.jpg',
    created_at: '2024-01-05',
  },
]

export async function GET() {
  return NextResponse.json({ success: true, data: videos })
}
