import { NextRequest, NextResponse } from 'next/server'
import { checkAdminAuth, requireAuth } from '@/lib/auth'

// 模拟数据存储 - 实际项目中应该使用数据库
let videos = [
  {
    id: '1',
    title: '《夜空中最亮的星》创作过程分享',
    description: '分享这首歌的创作灵感和制作过程，从词曲创作到录音制作的完整流程。',
    bilibili_url: 'https://www.bilibili.com/video/BV1234567890',
    bilibili_embed_id: 'BV1234567890',
    thumbnail_url: '/thumbnails/video1.jpg',
    created_at: '2024-01-15',
    updated_at: '2024-01-15',
  },
  {
    id: '2',
    title: '吉他弹唱《时光倒流》',
    description: '用吉他弹唱这首关于回忆的歌曲，希望能唤起大家心中美好的回忆。',
    bilibili_url: 'https://www.bilibili.com/video/BV2345678901',
    bilibili_embed_id: 'BV2345678901',
    thumbnail_url: '/thumbnails/video2.jpg',
    created_at: '2024-01-10',
    updated_at: '2024-01-10',
  },
  {
    id: '3',
    title: '音乐制作软件使用教程',
    description: '分享我常用的音乐制作软件和技巧，适合音乐制作初学者观看学习。',
    bilibili_url: 'https://www.bilibili.com/video/BV3456789012',
    bilibili_embed_id: 'BV3456789012',
    thumbnail_url: '/thumbnails/video3.jpg',
    created_at: '2024-01-05',
    updated_at: '2024-01-05',
  },
]

// 从B站链接提取BV号的函数
function extractBVFromUrl(url: string): string {
  const bvMatch = url.match(/BV[a-zA-Z0-9]+/)
  return bvMatch ? bvMatch[0] : ''
}

export async function GET(request: NextRequest) {
  const auth = checkAdminAuth(request)
  
  if (!auth.isAuthenticated) {
    return requireAuth()
  }

  return NextResponse.json({ success: true, data: videos })
}

export async function POST(request: NextRequest) {
  const auth = checkAdminAuth(request)
  
  if (!auth.isAuthenticated) {
    return requireAuth()
  }

  try {
    const body = await request.json()
    const { title, description, bilibili_url, thumbnail_url } = body

    if (!title || !bilibili_url) {
      return NextResponse.json(
        { error: 'Title and bilibili_url are required' },
        { status: 400 }
      )
    }

    const bilibili_embed_id = extractBVFromUrl(bilibili_url)
    
    if (!bilibili_embed_id) {
      return NextResponse.json(
        { error: 'Invalid Bilibili URL. Please provide a valid BV link.' },
        { status: 400 }
      )
    }

    const newVideo = {
      id: Date.now().toString(),
      title,
      description: description || '',
      bilibili_url,
      bilibili_embed_id,
      thumbnail_url: thumbnail_url || '',
      created_at: new Date().toISOString().split('T')[0],
      updated_at: new Date().toISOString().split('T')[0],
    }

    videos.push(newVideo)

    return NextResponse.json({ success: true, data: newVideo })
  } catch (error) {
    return NextResponse.json(
      { error: 'Invalid request body' },
      { status: 400 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  const auth = checkAdminAuth(request)
  
  if (!auth.isAuthenticated) {
    return requireAuth()
  }

  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'ID is required' },
        { status: 400 }
      )
    }

    const index = videos.findIndex(video => video.id === id)
    
    if (index === -1) {
      return NextResponse.json(
        { error: 'Video not found' },
        { status: 404 }
      )
    }

    videos.splice(index, 1)

    return NextResponse.json({ success: true })
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to delete video' },
      { status: 500 }
    )
  }
}
