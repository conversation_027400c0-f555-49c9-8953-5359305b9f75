import { NextRequest, NextResponse } from 'next/server'
import { checkAdminAuth, requireAuth } from '@/lib/auth'
import {
  getMusicVideos,
  addMusicVideo,
  updateMusicVideo,
  deleteMusicVideo
} from '@/lib/simple-store'

// 从B站URL中提取视频ID
function extractBilibiliId(url: string): string | null {
  try {
    // 支持多种B站URL格式
    const patterns = [
      /bilibili\.com\/video\/(BV[a-zA-Z0-9]+)/,
      /bilibili\.com\/video\/(av\d+)/,
      /b23\.tv\/([a-zA-Z0-9]+)/,
    ]

    for (const pattern of patterns) {
      const match = url.match(pattern)
      if (match) {
        return match[1]
      }
    }

    return null
  } catch (error) {
    return null
  }
}

// 从B站获取视频信息和缩略图
async function getBilibiliVideoInfo(bvid: string): Promise<{ title?: string; pic?: string } | null> {
  try {
    // 使用B站的公开API获取视频信息
    const response = await fetch(`https://api.bilibili.com/x/web-interface/view?bvid=${bvid}`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://www.bilibili.com/'
      }
    })

    if (!response.ok) {
      return null
    }

    const data = await response.json()

    if (data.code === 0 && data.data) {
      return {
        title: data.data.title,
        pic: data.data.pic
      }
    }

    return null
  } catch (error) {
    console.error('Error fetching Bilibili video info:', error)
    return null
  }
}

export async function GET(request: NextRequest) {
  const auth = checkAdminAuth(request)

  if (!auth.isAuthenticated) {
    return requireAuth()
  }

  const musicVideos = getMusicVideos()
  return NextResponse.json({ success: true, data: musicVideos })
}

export async function POST(request: NextRequest) {
  const auth = checkAdminAuth(request)

  if (!auth.isAuthenticated) {
    return requireAuth()
  }

  try {
    const body = await request.json()
    const { title, artist, description, bilibili_url, thumbnail_url } = body

    if (!title || !artist || !bilibili_url) {
      return NextResponse.json(
        { error: 'Title, artist, and bilibili_url are required' },
        { status: 400 }
      )
    }

    // 从B站URL中提取embed ID
    const bilibiliEmbedId = extractBilibiliId(bilibili_url)
    if (!bilibiliEmbedId) {
      return NextResponse.json(
        { error: 'Invalid Bilibili URL' },
        { status: 400 }
      )
    }

    // 如果没有提供缩略图，尝试从B站获取
    let finalThumbnailUrl = thumbnail_url || ''
    if (!finalThumbnailUrl) {
      const videoInfo = await getBilibiliVideoInfo(bilibiliEmbedId)
      if (videoInfo?.pic) {
        finalThumbnailUrl = videoInfo.pic
      }
    }

    const newVideo = {
      id: Date.now().toString(),
      title,
      artist,
      description: description || '',
      bilibili_url,
      bilibili_embed_id: bilibiliEmbedId,
      thumbnail_url: finalThumbnailUrl,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }

    addMusicVideo(newVideo)

    return NextResponse.json({ success: true, data: newVideo })
  } catch (error) {
    return NextResponse.json(
      { error: 'Invalid request body' },
      { status: 400 }
    )
  }
}

// 更新音乐视频
export async function PUT(request: NextRequest) {
  const auth = checkAdminAuth(request)

  if (!auth.isAuthenticated) {
    return requireAuth()
  }

  try {
    const body = await request.json()
    const { id, title, artist, description, bilibili_url, thumbnail_url } = body

    if (!id) {
      return NextResponse.json(
        { error: 'Video ID is required' },
        { status: 400 }
      )
    }

    const musicVideos = getMusicVideos()
    const video = musicVideos.find((video: any) => video.id === id)
    if (!video) {
      return NextResponse.json(
        { error: 'Video not found' },
        { status: 404 }
      )
    }

    if (!title || !artist || !bilibili_url) {
      return NextResponse.json(
        { error: 'Title, artist, and bilibili_url are required' },
        { status: 400 }
      )
    }

    // 从B站URL中提取embed ID
    const bilibiliEmbedId = extractBilibiliId(bilibili_url)
    if (!bilibiliEmbedId) {
      return NextResponse.json(
        { error: 'Invalid Bilibili URL' },
        { status: 400 }
      )
    }

    // 如果没有提供缩略图，尝试从B站获取
    let finalThumbnailUrl = thumbnail_url || ''
    if (!finalThumbnailUrl) {
      const videoInfo = await getBilibiliVideoInfo(bilibiliEmbedId)
      if (videoInfo?.pic) {
        finalThumbnailUrl = videoInfo.pic
      }
    }

    const updatedVideo = {
      ...video,
      title,
      artist,
      description: description || '',
      bilibili_url,
      bilibili_embed_id: bilibiliEmbedId,
      thumbnail_url: finalThumbnailUrl,
      updated_at: new Date().toISOString(),
    }

    updateMusicVideo(id, updatedVideo)

    return NextResponse.json({ success: true, data: updatedVideo })
  } catch (error) {
    return NextResponse.json(
      { error: 'Invalid request body' },
      { status: 400 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  const auth = checkAdminAuth(request)
  
  if (!auth.isAuthenticated) {
    return requireAuth()
  }

  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'ID is required' },
        { status: 400 }
      )
    }

    const musicVideos = getMusicVideos()
    const video = musicVideos.find((video: any) => video.id === id)

    if (!video) {
      return NextResponse.json(
        { error: 'Video not found' },
        { status: 404 }
      )
    }

    deleteMusicVideo(id)

    return NextResponse.json({ success: true })
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to delete track' },
      { status: 500 }
    )
  }
}
