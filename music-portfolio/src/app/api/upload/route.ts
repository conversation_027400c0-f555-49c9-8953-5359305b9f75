import { NextRequest, NextResponse } from 'next/server'
import { checkAdminAuth, requireAuth } from '@/lib/auth'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'

export async function POST(request: NextRequest) {
  const auth = checkAdminAuth(request)
  
  if (!auth.isAuthenticated) {
    return requireAuth()
  }

  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    const type = formData.get('type') as string // 'poster' or 'thumbnail'

    if (!file) {
      return NextResponse.json(
        { error: 'No file uploaded' },
        { status: 400 }
      )
    }

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only JPEG, PNG, and WebP are allowed.' },
        { status: 400 }
      )
    }

    // 验证文件大小 (5MB)
    const maxSize = 5 * 1024 * 1024
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'File too large. Maximum size is 5MB.' },
        { status: 400 }
      )
    }

    // 生成文件名
    const timestamp = Date.now()
    const extension = file.name.split('.').pop()
    const fileName = `${type}_${timestamp}.${extension}`

    // 确定上传目录
    const uploadDir = type === 'poster' ? 'posters' : 'thumbnails'
    const publicDir = join(process.cwd(), 'public', uploadDir)
    
    // 确保目录存在
    try {
      await mkdir(publicDir, { recursive: true })
    } catch (error) {
      // 目录可能已存在，忽略错误
    }

    // 保存文件
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    const filePath = join(publicDir, fileName)
    
    await writeFile(filePath, buffer)

    // 返回文件URL
    const fileUrl = `/${uploadDir}/${fileName}`

    return NextResponse.json({
      success: true,
      url: fileUrl,
      filename: fileName
    })

  } catch (error) {
    console.error('Upload error:', error)
    return NextResponse.json(
      { error: 'Failed to upload file' },
      { status: 500 }
    )
  }
}
