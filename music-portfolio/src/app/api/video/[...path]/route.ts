import { NextRequest, NextResponse } from 'next/server'
import { readFile, stat } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'
import { checkAdminAuth, requireAuth } from '@/lib/auth'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  // 等待 params 解析
  const resolvedParams = await params
  try {
    // 检查管理员权限 - 支持多种认证方式
    let auth = checkAdminAuth(request)

    // 如果header认证失败，尝试从查询参数获取认证信息
    if (!auth.isAuthenticated) {
      const { searchParams } = new URL(request.url)
      const authParam = searchParams.get('auth')
      if (authParam) {
        try {
          const credentials = atob(decodeURIComponent(authParam))
          const [username, password] = credentials.split(':')
          if (username === 'admin' && password === 'admin123') {
            auth = { isAuthenticated: true, username }
          }
        } catch (e) {
          // 忽略解码错误
        }
      }
    }

    if (!auth.isAuthenticated) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    // 构建文件路径
    const filePath = resolvedParams.path.join('/')
    const fullPath = path.join(process.cwd(), 'public', 'uploads', 'movies', filePath)

    // 检查文件是否存在
    if (!existsSync(fullPath)) {
      return NextResponse.json(
        { error: 'Video file not found' },
        { status: 404 }
      )
    }

    // 获取文件信息
    const fileStats = await stat(fullPath)
    const fileSize = fileStats.size

    // 处理Range请求（用于视频流）
    const range = request.headers.get('range')
    
    if (range) {
      // 解析Range头
      const parts = range.replace(/bytes=/, '').split('-')
      const start = parseInt(parts[0], 10)
      const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1
      const chunkSize = (end - start) + 1

      // 读取文件片段
      const fileBuffer = await readFile(fullPath)
      const chunk = fileBuffer.slice(start, end + 1)

      // 返回部分内容
      return new NextResponse(new Uint8Array(chunk), {
        status: 206,
        headers: {
          'Content-Range': `bytes ${start}-${end}/${fileSize}`,
          'Accept-Ranges': 'bytes',
          'Content-Length': chunkSize.toString(),
          'Content-Type': getContentType(fullPath),
          'Cache-Control': 'no-cache',
        },
      })
    } else {
      // 返回完整文件
      const fileBuffer = await readFile(fullPath)

      return new NextResponse(new Uint8Array(fileBuffer), {
        status: 200,
        headers: {
          'Content-Length': fileSize.toString(),
          'Content-Type': getContentType(fullPath),
          'Accept-Ranges': 'bytes',
          'Cache-Control': 'no-cache',
        },
      })
    }

  } catch (error) {
    console.error('Error serving video:', error)
    return NextResponse.json(
      { error: 'Failed to serve video file' },
      { status: 500 }
    )
  }
}

// 根据文件扩展名获取Content-Type
function getContentType(filePath: string): string {
  const ext = path.extname(filePath).toLowerCase()
  
  switch (ext) {
    case '.mp4':
      return 'video/mp4'
    case '.mov':
      return 'video/quicktime'
    case '.avi':
      return 'video/x-msvideo'
    case '.webm':
      return 'video/webm'
    case '.wmv':
      return 'video/x-ms-wmv'
    case '.flv':
      return 'video/x-flv'
    default:
      return 'video/mp4'
  }
}
