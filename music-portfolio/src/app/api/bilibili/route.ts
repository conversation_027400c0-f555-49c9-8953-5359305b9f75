import { NextRequest, NextResponse } from 'next/server'

// 从B站URL中提取视频ID
function extractBilibiliId(url: string): string | null {
  try {
    // 支持多种B站URL格式
    const patterns = [
      /bilibili\.com\/video\/(BV[a-zA-Z0-9]+)/,
      /bilibili\.com\/video\/(av\d+)/,
      /b23\.tv\/([a-zA-Z0-9]+)/,
    ]

    for (const pattern of patterns) {
      const match = url.match(pattern)
      if (match) {
        return match[1]
      }
    }

    return null
  } catch (error) {
    return null
  }
}

// 从B站获取视频信息和缩略图
async function getBilibiliVideoInfo(bvid: string): Promise<{ title?: string; pic?: string; desc?: string } | null> {
  try {
    // 使用B站的公开API获取视频信息
    const response = await fetch(`https://api.bilibili.com/x/web-interface/view?bvid=${bvid}`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://www.bilibili.com/'
      }
    })

    if (!response.ok) {
      return null
    }

    const data = await response.json()
    
    if (data.code === 0 && data.data) {
      return {
        title: data.data.title,
        pic: data.data.pic,
        desc: data.data.desc
      }
    }

    return null
  } catch (error) {
    console.error('Error fetching Bilibili video info:', error)
    return null
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const url = searchParams.get('url')

    if (!url) {
      return NextResponse.json(
        { error: 'URL parameter is required' },
        { status: 400 }
      )
    }

    // 提取视频ID
    const bvid = extractBilibiliId(url)
    if (!bvid) {
      return NextResponse.json(
        { error: 'Invalid Bilibili URL' },
        { status: 400 }
      )
    }

    // 获取视频信息
    const videoInfo = await getBilibiliVideoInfo(bvid)
    if (!videoInfo) {
      return NextResponse.json(
        { error: 'Failed to fetch video information. The video may not exist or be private.' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        bvid,
        title: videoInfo.title,
        thumbnail: videoInfo.pic,
        description: videoInfo.desc
      }
    })
  } catch (error) {
    console.error('Error in Bilibili API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
