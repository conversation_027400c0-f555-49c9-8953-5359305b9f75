'use client'

import { useState, useEffect } from 'react'

export default function TestPage() {
  const [musicData, setMusicData] = useState<any[]>([])
  const [movieData, setMovieData] = useState<any[]>([])
  const [adminMusicData, setAdminMusicData] = useState<any[]>([])
  const [adminMovieData, setAdminMovieData] = useState<any[]>([])
  const [testResults, setTestResults] = useState<string[]>([])

  useEffect(() => {
    runTests()
  }, [])

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, message])
  }

  const runTests = async () => {
    addTestResult('🧪 开始测试数据同步...')

    try {
      // 1. 测试公开API
      addTestResult('1. 测试公开API')
      
      const musicResponse = await fetch('/api/music')
      const musicData = await musicResponse.json()
      setMusicData(musicData.data)
      addTestResult(`Music API 返回: ${musicData.data.length} 个视频`)
      
      const movieResponse = await fetch('/api/movies')
      const movieData = await movieResponse.json()
      setMovieData(movieData.data)
      addTestResult(`Movie API 返回: ${movieData.data.length} 部电影`)

      // 2. 测试管理API（需要认证）
      addTestResult('2. 测试管理API')
      
      const credentials = btoa('admin:admin123')
      const authHeaders = {
        'Authorization': `Basic ${credentials}`
      }

      const adminMusicResponse = await fetch('/api/admin/music', {
        headers: authHeaders
      })
      const adminMusicData = await adminMusicResponse.json()
      setAdminMusicData(adminMusicData.data)
      addTestResult(`Admin Music API 返回: ${adminMusicData.data.length} 个视频`)

      const adminMovieResponse = await fetch('/api/admin/movies', {
        headers: authHeaders
      })
      const adminMovieData = await adminMovieResponse.json()
      setAdminMovieData(adminMovieData.data)
      addTestResult(`Admin Movie API 返回: ${adminMovieData.data.length} 部电影`)

      // 3. 验证数据一致性
      addTestResult('3. 验证数据一致性')
      const musicConsistent = musicData.data.length === adminMusicData.data.length
      const movieConsistent = movieData.data.length === adminMovieData.data.length
      
      addTestResult(`Music数据一致性: ${musicConsistent ? '✅' : '❌'}`)
      addTestResult(`Movie数据一致性: ${movieConsistent ? '✅' : '❌'}`)
      
      addTestResult('✅ 测试完成')

    } catch (error) {
      addTestResult(`❌ 测试失败: ${error}`)
    }
  }

  const testAddMusic = async () => {
    try {
      const credentials = btoa('admin:admin123')
      const response = await fetch('/api/admin/music', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${credentials}`
        },
        body: JSON.stringify({
          title: '测试音乐视频',
          artist: 'KIMAHALA',
          description: '这是一个测试视频',
          bilibili_url: 'https://www.bilibili.com/video/BV1234567890',
          thumbnail_url: '/thumbnails/test.jpg'
        })
      })

      if (response.ok) {
        addTestResult('✅ 成功添加测试音乐视频')
        // 重新获取数据
        setTimeout(() => {
          window.location.reload()
        }, 1000)
      } else {
        addTestResult('❌ 添加音乐视频失败')
      }
    } catch (error) {
      addTestResult(`❌ 添加音乐视频错误: ${error}`)
    }
  }

  const testAddMovie = async () => {
    try {
      const credentials = btoa('admin:admin123')
      const response = await fetch('/api/admin/movies', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${credentials}`
        },
        body: JSON.stringify({
          title: '测试电影',
          description: '这是一个测试电影',
          category: 'drama',
          rating: 8.5,
          release_year: 2024
        })
      })

      if (response.ok) {
        addTestResult('✅ 成功添加测试电影')
        // 重新获取数据
        setTimeout(() => {
          window.location.reload()
        }, 1000)
      } else {
        addTestResult('❌ 添加电影失败')
      }
    } catch (error) {
      addTestResult(`❌ 添加电影错误: ${error}`)
    }
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">数据同步测试页面</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
          <div className="bg-gray-800 p-6 rounded-lg">
            <h2 className="text-xl font-semibold mb-4">测试操作</h2>
            <div className="space-y-4">
              <button
                onClick={testAddMusic}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
              >
                添加测试音乐视频
              </button>
              <button
                onClick={testAddMovie}
                className="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded"
              >
                添加测试电影
              </button>
              <button
                onClick={() => window.location.reload()}
                className="w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded"
              >
                重新测试
              </button>
            </div>
          </div>

          <div className="bg-gray-800 p-6 rounded-lg">
            <h2 className="text-xl font-semibold mb-4">数据统计</h2>
            <div className="space-y-2">
              <p>公开Music API: {musicData.length} 个视频</p>
              <p>管理Music API: {adminMusicData.length} 个视频</p>
              <p>公开Movie API: {movieData.length} 部电影</p>
              <p>管理Movie API: {adminMovieData.length} 部电影</p>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">测试日志</h2>
          <div className="bg-gray-900 p-4 rounded font-mono text-sm max-h-96 overflow-y-auto">
            {testResults.map((result, index) => (
              <div key={index} className="mb-1">
                {result}
              </div>
            ))}
          </div>
        </div>

        <div className="mt-8 text-center">
          <p className="text-gray-400">
            测试完成后，请访问 <a href="/music" className="text-blue-400 hover:underline">/music</a> 和 <a href="/movie" className="text-blue-400 hover:underline">/movie</a> 页面验证数据同步
          </p>
        </div>
      </div>
    </div>
  )
}
