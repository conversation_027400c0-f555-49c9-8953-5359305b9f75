#!/bin/bash

echo "🔧 修复构建错误脚本"
echo "==================="

echo "1. 已修复的问题 ✅"
echo "   - API 路由类型错误: video/[...path]/route.ts"
echo "   - React hooks 导入: video/page.tsx"
echo "   - iframe 属性问题: music/page.tsx, video/page.tsx"
echo "   - TypeScript 字段缺失: admin/page.tsx"
echo "   - find() 方法类型: admin/movies/route.ts, admin/music/route.ts"

echo ""
echo "2. 清理构建缓存..."
rm -rf .next
rm -rf node_modules/.cache 2>/dev/null || true

echo "3. 测试构建..."
npm run build

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 构建成功！所有错误已修复"
    echo ""
    echo "🎯 修复总结:"
    echo "================================="
    echo "✅ API 路由类型: 更新为 Next.js 15 兼容格式"
    echo "✅ React hooks: 添加缺失的 useEffect 导入"
    echo "✅ iframe 属性: 移除废弃的 border 属性"
    echo "✅ find() 方法: 添加明确的类型注解"
    echo "✅ TypeScript: 修复所有类型错误"
    echo ""
    echo "🚀 现在可以部署到远程服务器了！"
    echo ""
    echo "📋 部署命令:"
    echo "1. 在远程服务器运行: ./final-start.sh"
    echo "2. 或者手动运行: npm run build && npm start"
    echo ""
    echo "🎊 恭喜！项目已准备好部署！"
    
else
    echo ""
    echo "❌ 构建仍然失败"
    echo ""
    echo "📋 请检查以下内容:"
    echo "1. 是否还有其他 TypeScript 错误"
    echo "2. 是否有语法错误"
    echo "3. 是否有导入错误"
    echo ""
    echo "🔧 调试命令:"
    echo "- 详细构建: npm run build --verbose"
    echo "- 类型检查: npx tsc --noEmit"
    echo "- 语法检查: npm run lint"
    
    exit 1
fi
