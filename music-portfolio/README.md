# 马君个人音乐视频作品展示网站

这是一个基于 Next.js 构建的个人音乐和视频作品展示网站，参考了 Joanne <PERSON> Taylor 官网的设计风格。

## 功能特性

### 前台展示
- **主页 (Home)**: 个人简介和作品展示
- **音乐 (Music)**: 音频播放器和音乐作品列表
- **视频 (Video)**: B站视频嵌入播放

### 后台管理
- **音乐管理**: 添加、编辑、删除音乐作品
- **视频管理**: 添加、编辑、删除视频作品
- **个人信息管理**: 编辑主页展示内容
- **权限认证**: 基本的用户名密码认证

## 技术栈

- **前端框架**: Next.js 15 (React 18)
- **样式**: Tailwind CSS
- **数据库**: Supabase (可选)
- **音频播放**: React Audio Player
- **图标**: Lucide React
- **部署**: Vercel

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 环境配置

复制环境变量模板文件：

```bash
cp .env.local.example .env.local
```

编辑 `.env.local` 文件，配置以下变量：

```env
# Supabase配置 (可选)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SU.PABASE_ANON_KEY=your_supabase_anon_key

# 管理员认证
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_admin_password
```

### 3. 启动开发服务器

```bash
npm run dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看网站。

### 4. 访问后台管理

访问 [http://localhost:3000/admin](http://localhost:3000/admin) 进入后台管理系统。

默认管理员账号：
- 用户名: admin
- 密码: 在 `.env.local` 中设置的 `ADMIN_PASSWORD`

## 文件结构

```
src/
├── app/                    # Next.js App Router
│   ├── admin/             # 后台管理页面
│   ├── api/               # API 路由
│   ├── music/             # 音乐页面
│   ├── video/             # 视频页面
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 主页
├── components/            # 可复用组件
│   ├── Navigation.tsx     # 导航栏
│   └── Footer.tsx         # 页脚
└── lib/                   # 工具函数
    ├── supabase.ts        # 数据库配置
    └── auth.ts            # 认证逻辑
```

## 自定义配置

### 1. 添加背景图片

将电吉他背景图片命名为 `electric-guitar-bg.jpg` 并放置在 `public/` 目录下。
- 建议尺寸：1920x1080 或更高分辨率
- 图片应该是酷炫的电吉他照片，适合作为网站背景

### 2. 添加音乐文件

将音乐文件放置在 `public/music/` 目录下，支持格式：
- MP3
- WAV
- OGG

### 3. 修改个人信息

编辑 `src/app/page.tsx` 中的个人信息内容

### 4. 配置B站视频

在视频管理中添加B站视频的BV号

### 5. 更新B站链接

在以下文件中更新B站链接：
- `src/components/Navigation.tsx` - 导航栏B站图标
- `src/components/Footer.tsx` - 页脚B站链接
- `src/app/video/page.tsx` - 视频页面B站链接

当前B站链接：https://space.bilibili.com/27744192

## 部署

### Vercel 部署

1. 将代码推送到 GitHub
2. 在 Vercel 中导入项目
3. 配置环境变量
4. 部署

### 其他平台

项目支持部署到任何支持 Node.js 的平台。

## 开发说明

### 添加新功能

1. 在相应目录下创建组件或页面
2. 更新导航菜单（如需要）
3. 添加相应的 API 路由（如需要）

### 数据库集成

目前使用模拟数据，如需集成真实数据库：

1. 配置 Supabase 或其他数据库
2. 更新 `src/lib/supabase.ts` 中的数据库操作
3. 在页面组件中调用相应的 API

## 许可证

MIT License
