# 🔐 管理员配置指南

## 📍 配置文件位置

管理员登录信息配置在项目根目录的 `.env.local` 文件中。

**注意**: `.env.local` 是隐藏文件（以 `.` 开头），在某些文件管理器中可能不显示。

## 🔧 当前配置

查看当前配置：
```bash
cat .env.local
```

## ✏️ 修改管理员信息

### 1. 编辑配置文件

```bash
# 使用 nano 编辑器
nano .env.local

# 或使用 vim 编辑器
vim .env.local

# 或使用其他文本编辑器
```

### 2. 配置内容

```env
# 管理员认证
ADMIN_USERNAME=你的用户名
ADMIN_PASSWORD=你的安全密码

# Supabase配置 (可选 - 目前使用模拟数据)
# NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
# NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 3. 密码安全建议

- 使用至少 12 位字符
- 包含大小写字母、数字和特殊字符
- 避免使用常见密码
- 定期更换密码

示例强密码：
```env
ADMIN_PASSWORD=MySecure@Pass2024!
```

## 🚀 应用配置更改

修改 `.env.local` 后需要重启服务器：

### 开发环境
```bash
# 停止开发服务器 (Ctrl+C)
# 然后重新启动
npm run dev
```

### 生产环境
```bash
# 重新构建和启动
npm run build
npm start

# 或使用 PM2
pm2 restart music-portfolio
```

## 🔍 验证配置

1. **访问管理页面**: http://localhost:3000/admin
2. **使用新的用户名和密码登录**
3. **确认能够正常访问管理功能**

## 🛡️ 安全注意事项

### 1. 文件权限
确保 `.env.local` 文件权限正确：
```bash
chmod 600 .env.local
```

### 2. 版本控制
`.env.local` 文件不应该提交到 Git 仓库：
```bash
# 检查 .gitignore 文件是否包含
echo ".env.local" >> .gitignore
```

### 3. 远程服务器部署
在远程服务器上，确保：
- 创建 `.env.local` 文件
- 设置正确的管理员信息
- 文件权限设置为 600

```bash
# 在远程服务器上创建配置文件
cat > .env.local << 'EOF'
# 管理员认证
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_secure_password_here
EOF

# 设置文件权限
chmod 600 .env.local
```

## 🔄 重置密码

如果忘记密码，可以：

1. **直接编辑 `.env.local` 文件**
2. **重启服务器**
3. **使用新密码登录**

## 📱 多用户支持

当前系统只支持单个管理员账户。如需多用户支持，需要：

1. 修改认证逻辑
2. 使用数据库存储用户信息
3. 实现用户管理功能

## 🆘 常见问题

### Q: 修改密码后无法登录？
A: 确保重启了服务器，并检查 `.env.local` 文件格式是否正确。

### Q: `.env.local` 文件不存在？
A: 从 `.env.local.example` 复制：
```bash
cp .env.local.example .env.local
```

### Q: 在远程服务器上看不到 `.env.local`？
A: 使用 `ls -la` 命令查看隐藏文件：
```bash
ls -la | grep .env
```

## 📞 技术支持

如果遇到配置问题，请检查：
1. 文件是否存在
2. 文件权限是否正确
3. 服务器是否已重启
4. 浏览器缓存是否已清除
