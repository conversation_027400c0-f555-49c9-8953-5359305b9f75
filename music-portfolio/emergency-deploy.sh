#!/bin/bash

echo "🚨 应急部署脚本 - 最简化版本"
echo "================================"

# 检查当前目录
if [ ! -f "package.json" ]; then
    echo "错误: 请在项目根目录运行此脚本"
    exit 1
fi

echo "1. 备份原始文件..."
cp package.json package.json.backup
cp src/app/page.tsx src/app/page.tsx.backup 2>/dev/null || true

echo "2. 使用最简化配置..."
cp package.minimal.json package.json
cp src/app/page.simple.tsx src/app/page.tsx

echo "3. 清理项目..."
rm -rf node_modules package-lock.json .next
npm cache clean --force 2>/dev/null || true

echo "4. 检查 Node.js 版本..."
node_version=$(node --version 2>/dev/null || echo "未安装")
echo "Node.js 版本: $node_version"

if [ "$node_version" = "未安装" ]; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    echo "运行: ./fix-deployment.sh"
    exit 1
fi

echo "5. 安装依赖（使用兼容模式）..."
npm install --legacy-peer-deps --no-optional

if [ $? -ne 0 ]; then
    echo "❌ 依赖安装失败，尝试强制安装..."
    npm install --force --legacy-peer-deps --no-optional
    
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装彻底失败"
        echo "恢复原始文件..."
        mv package.json.backup package.json 2>/dev/null || true
        mv src/app/page.tsx.backup src/app/page.tsx 2>/dev/null || true
        exit 1
    fi
fi

echo "6. 构建项目..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ 构建成功！"
    
    # 停止可能运行的旧进程
    if [ -f "app.pid" ]; then
        old_pid=$(cat app.pid)
        kill $old_pid 2>/dev/null || true
        rm app.pid
    fi
    
    echo "7. 启动应用..."
    nohup npm start > app.log 2>&1 &
    echo $! > app.pid
    
    sleep 3
    
    # 检查应用是否正常启动
    if ps -p $(cat app.pid) > /dev/null; then
        echo "✅ 应用启动成功！"
        echo ""
        echo "📊 部署信息:"
        echo "- PID: $(cat app.pid)"
        echo "- 日志: tail -f app.log"
        echo "- 访问: http://$(hostname -I | awk '{print $1}'):3000"
        echo "- 停止: kill $(cat app.pid)"
        echo ""
        echo "🎉 应急部署完成！"
        
        # 显示最近的日志
        echo "📋 最近日志:"
        tail -n 10 app.log
        
    else
        echo "❌ 应用启动失败"
        echo "查看日志: cat app.log"
        exit 1
    fi
    
else
    echo "❌ 构建失败"
    echo "恢复原始文件..."
    mv package.json.backup package.json 2>/dev/null || true
    mv src/app/page.tsx.backup src/app/page.tsx 2>/dev/null || true
    exit 1
fi
