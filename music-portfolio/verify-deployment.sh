#!/bin/bash

echo "🔍 验证部署环境..."

# 检查 Node.js 版本
echo "📋 检查 Node.js 版本..."
NODE_VERSION=$(node --version)
echo "Node.js 版本: $NODE_VERSION"

# 检查 npm 版本
NPM_VERSION=$(npm --version)
echo "npm 版本: $NPM_VERSION"

# 检查必要的文件
echo ""
echo "📁 检查必要文件..."
files=("package.json" "tailwind.config.ts" "postcss.config.mjs" "src/app/globals.css")
for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file 存在"
    else
        echo "❌ $file 不存在"
        exit 1
    fi
done

# 检查依赖安装
echo ""
echo "📦 检查关键依赖..."
dependencies=("next" "react" "tailwindcss" "postcss" "autoprefixer")
for dep in "${dependencies[@]}"; do
    if npm list "$dep" --depth=0 >/dev/null 2>&1; then
        version=$(npm list "$dep" --depth=0 2>/dev/null | grep "$dep@" | sed 's/.*@//')
        echo "✅ $dep@$version"
    else
        echo "❌ $dep 未安装"
        exit 1
    fi
done

# 测试构建
echo ""
echo "🏗️ 测试构建..."
if npm run build >/dev/null 2>&1; then
    echo "✅ 构建成功"
else
    echo "❌ 构建失败"
    echo "运行 'npm run build' 查看详细错误信息"
    exit 1
fi

echo ""
echo "🎉 所有检查通过！项目已准备好部署。"
echo ""
echo "🚀 启动命令："
echo "npm start"
