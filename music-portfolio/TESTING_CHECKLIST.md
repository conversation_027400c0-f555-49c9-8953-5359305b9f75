# 功能测试清单

## 🎬 电影上传和播放功能测试

### 1. 视频上传测试
- [ ] 访问 `/admin` 页面并登录
- [ ] 切换到"电影管理"标签
- [ ] 点击"添加新电影"
- [ ] 填写基本信息（标题、描述、分类）
- [ ] 测试视频文件上传：
  - [ ] 选择小于10GB的视频文件
  - [ ] 验证支持的格式：MP4, MOV, AVI, WebM, WMV, FLV
  - [ ] 观察实时上传进度（百分比显示）
  - [ ] 确认上传成功提示
- [ ] 保存电影信息
- [ ] 验证数据库中保存了video_file_path

### 2. 视频播放测试
- [ ] 访问 `/movie` 页面
- [ ] 确认已上传视频的电影显示"观看影片"按钮
- [ ] 未登录状态：按钮显示"需要登录"
- [ ] 登录后：按钮变为可点击的"观看影片"
- [ ] 点击播放按钮：
  - [ ] 视频在全屏模式播放
  - [ ] 播放控制正常（播放/暂停、进度条、音量）
  - [ ] 可以正常关闭播放器

### 3. 错误处理测试
- [ ] 上传超过10GB的文件（应显示错误）
- [ ] 上传不支持的格式（应显示错误）
- [ ] 网络中断时的处理
- [ ] 播放不存在的视频文件

## 🎵 音乐页面播放按钮测试

### 1. 播放按钮显示
- [ ] 访问 `/music` 页面
- [ ] 确认所有视频缩略图上都显示红色播放按钮
- [ ] 播放按钮不需要鼠标悬停即可看到
- [ ] 鼠标悬停时背景变深

### 2. 播放功能
- [ ] 点击播放按钮能正常播放B站视频
- [ ] 视频播放器正常加载
- [ ] 外部链接按钮正常工作

## 🔧 技术验证

### 1. 文件存储
- [ ] 检查 `public/uploads/movies/` 目录
- [ ] 验证文件命名格式：`movie_[timestamp].[ext]`
- [ ] 确认文件权限正确

### 2. API端点
- [ ] `POST /api/upload/video` - 上传功能
- [ ] `GET /api/video/[filename]` - 视频流
- [ ] 认证保护正常工作

### 3. 数据库
- [ ] 电影记录包含 `video_file_path` 字段
- [ ] 更新和删除功能正常
- [ ] 数据持久化正常

## 🚀 性能测试

### 1. 大文件上传
- [ ] 测试1GB以上文件上传
- [ ] 验证进度显示准确性
- [ ] 检查内存使用情况

### 2. 视频播放
- [ ] 大文件播放流畅性
- [ ] Range请求支持
- [ ] 缓存机制

## 📱 用户体验测试

### 1. 界面响应
- [ ] 上传进度条动画流畅
- [ ] 按钮状态变化清晰
- [ ] 错误提示友好

### 2. 移动端适配
- [ ] 手机浏览器播放正常
- [ ] 触摸操作响应
- [ ] 界面布局适配

## 🔒 安全测试

### 1. 认证保护
- [ ] 未登录用户无法上传
- [ ] 未登录用户无法观看视频
- [ ] 认证过期处理

### 2. 文件安全
- [ ] 文件类型验证
- [ ] 路径遍历防护
- [ ] 文件大小限制

## 📋 测试结果记录

### 通过的测试
- [ ] 视频上传功能
- [ ] 实时进度显示
- [ ] 视频播放功能
- [ ] 音乐页面播放按钮显示
- [ ] 认证保护
- [ ] 错误处理

### 发现的问题
- [ ] 问题1：描述...
- [ ] 问题2：描述...
- [ ] 问题3：描述...

### 需要改进的地方
- [ ] 改进1：描述...
- [ ] 改进2：描述...
- [ ] 改进3：描述...

## 📝 测试环境

- **浏览器**: Chrome/Safari/Firefox
- **操作系统**: macOS/Windows/Linux
- **网络环境**: 本地开发/生产环境
- **测试日期**: ___________
- **测试人员**: ___________
